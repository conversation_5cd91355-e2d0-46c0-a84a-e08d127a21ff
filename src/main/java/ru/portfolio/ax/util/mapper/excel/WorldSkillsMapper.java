package ru.portfolio.ax.util.mapper.excel;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import ru.portfolio.ax.model.GIAWorldskills;
import ru.portfolio.ax.repository.ref.DataSourceRefRepository;
import ru.portfolio.ax.rest.dto.AttachmentsDTO;
import ru.portfolio.ax.util.excel.ExcelDTO;

import java.util.ArrayList;
import java.util.List;

import static java.util.Objects.nonNull;
import static ru.portfolio.ax.util.excel.ExcelUtils.*;

@Service
@RequiredArgsConstructor
public class WorldSkillsMapper {

    private final DataSourceRefRepository dataSourceRefRepository;

    public GIAWorldskills map(List<String> fieldList, List<String> headers,
                              ExcelDTO excelDTO, List<String> stopWords, boolean isEmployee) {
        GIAWorldskills giaWorldskills = new GIAWorldskills();
        List<AttachmentsDTO.Comment> errorComments = new ArrayList<>();
        List<AttachmentsDTO.Comment> nonNullComments = new ArrayList<>();

        giaWorldskills.setCompetenceCode(tryToParseString(stopWords, fieldList.get(11), headers.get(11), errorComments));
        giaWorldskills.setName(tryToParseString(stopWords, fieldList.get(12), headers.get(12), errorComments, nonNullComments));
        giaWorldskills.setResultScore(tryToParseFloat(fieldList.get(13), headers.get(13), errorComments, nonNullComments));
        giaWorldskills.setMaxCompetenceScore(tryToParseFloat(fieldList.get(14), headers.get(14), errorComments, nonNullComments));
        giaWorldskills.setResultDate(tryToParseLocalDate(fieldList.get(15), headers.get(15), errorComments));
        giaWorldskills.setKod(tryToParseString(stopWords, fieldList.get(16), headers.get(16), errorComments, nonNullComments));
        String isEarlyReleaseStr = tryToParseString(stopWords, fieldList.get(17), headers.get(17), errorComments, nonNullComments);
        if (nonNull(isEarlyReleaseStr)) {
            boolean isEarlyRelease = isEarlyReleaseStr.toLowerCase().equals("да");
            giaWorldskills.setIsEarlyRelease(isEarlyRelease);
        }
        giaWorldskills.setSource(dataSourceRefRepository.findById(12).orElse(null));

        if (hasErrors(excelDTO, errorComments, nonNullComments)) {
            return null;
        }
        return giaWorldskills;
    }
}
