package ru.portfolio.ax.util;

import jodd.util.StringUtil;
import lombok.SneakyThrows;
import org.apache.commons.math3.util.Precision;
import org.springframework.data.util.Pair;
import ru.portfolio.ax.model.AverageAcademicPerformance;
import ru.portfolio.ax.model.PersonalDiagnosticDTO;
import ru.portfolio.ax.model.dto.*;
import ru.portfolio.ax.rest.dto.*;

import java.sql.Array;
import java.sql.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

public class ClickHouseMapper {
    public static final String ACHIEVEMENTS_SELECT = "select " +
            " achievement_name, " +
            " event_date, " +
            " achievement_activity_completion_date, " +
            " achievement_type, " +
            " achievement_source, " +
            " achievement_category, " +
            " achievement_description, " +
            " achievement_activity_format, " +
            " event_time, " +
            " record_id from ";

    public static final String PROGRESS_SELECT = "select " +
            "marked_subject_id, " +
            "marked_subject_name, " +
            "mark_value_5, " +
            "mark_value_100, " +
            "mark_weight, " +
            "school_id, " +
            "event_date, " +
            "marked_subject_id, " +
            "marked_subject_name, " +
            "lesson_absence_subject_id, " +
            "lesson_absence_subject_name, " +
            "lesson_subject_id, " +
            "lesson_subject_name, " +
            "learning_profile from ";

    public static final String CULTURAL_SELECT = "select " +
            "record_id, " +
            "person_id, " +
            "event_type, " +
            "person_type, " +
            "school_id, " +
            "event_date, " +
            "event_time, " +
            "cultural_institution_name, " +
            "cultural_institution_type from ";

    public static final String SELF_DIAGNOSTIC_SELECT = "select " +
            "self_diagnostic_work_id, " +
            "self_diagnostic_work_name, " +
            "self_diagnostic_work_type, " +
            "self_diagnostic_subject_id, " +
            "self_diagnostic_subject_name, " +
            "self_diagnostic_parallel, " +
            "self_diagnostic_attempt_number, " +
            "self_diagnostic_fact_execution_duration, " +
            "self_diagnostic_plan_execution_duration, " +
            "self_diagnostic_base_task_earned_score_count, " +
            "self_diagnostic_profile_task_earned_score_count, " +
            "self_diagnostic_work_completion, " +
            "self_diagnostic_percent_execution_result, " +
            "event_date from ";

    public static final String PERSONAL_DIAGNOSTIC_SELECT = "select " +
            "record_id, " +
            "person_id, " +
            "self_diagnostic_work_id, " +
            "self_diagnostic_subject_name, " +
            "self_diagnostic_work_name, " +
            "self_diagnostic_execution_result_mark_value, " +
            "self_diagnostic_percent_execution_result, " +
            "event_date from ";


    public static final String DIAGNOSTIC_SELECT = "select " +
            "person_id, " +
            "self_diagnostic_work_id, " +
            "self_diagnostic_subject_name, " +
            "self_diagnostic_max_execution_result, " +
            "school_id, " +
            "mark_value_5, " +
            "event_type, " +
            "event_date from ";

    public static final String SHORT_DIAGNOSTIC_SELECT = "select " +
            "person_id, " +
            "record_id, " +
            "creation_date, " +
            "self_diagnostic_max_execution_result, " +
            "note, " +
            "event_type, " +
            "event_date from ";

    public static final String INDEPENDENT_DIAGNOSTIC_SELECT = "select " +
            "self_diagnostic_subject_name, " +
            "school_id, " +
            "self_diagnostic_work_name, " +
            "self_diagnostic_max_execution_result, " +
            "self_diagnostic_execution_result_mark_value, " +
            "self_diagnostic_percent_execution_result, " +
            "self_diagnostic_level_type, " +
            "event_date, " +
            "person_id, " +
            "record_id, " +
            "note, " +
            "mark_value_5, " +
            "self_diagnostic_work_id from ";

    public static final String LESSON_SELECT = "select " +
            "marked_lesson_id, " +
            "mark_value_5, " +
            "mark_weight from ";

    public static final String ERR_ENTITY_SELECT = "select " +
            "event_type, " +
            "record_id, " +
            "event_time, " +
            "event_date, " +
            "achievement_category, " +
            "olimpiad_name, " +
            "achievement_name, " +
            "cultural_institution_name, " +
            "person_id from ";

    public static final String ERR_ENTITY_PROFTECH_SELECT = "select " +
            "event_type, " +
            "record_id, " +
            "event_time, " +
            "event_date from ";

    public static final String CERTIFICATE_SELECT = "select " +
            "additional_education_certificates_type, " +
            "additional_education_certificates_number, " +
            "additional_education_certificates_organization, " +
            "additional_education_certificates_organization_id, " +
            "additional_education_certificates_profession, " +
            "additional_education_certificates_programm, " +
            "additional_education_certificates_qualification, " +
            "event_time, " +
            "event_date from ";

    public static final String OLYMPIAD_GRATITUDE_SELECT = "select " +
            "olimpiad_name, olimpiad_result from ";

    @SneakyThrows
    public static AverageAcademicPerformanceDTO mapAveragePerformance(ResultSet resultSet) {
        AverageAcademicPerformanceDTO resultDTO = new AverageAcademicPerformanceDTO();

        List<AverageAcademicPerformance> models = new ArrayList<>();
        while (resultSet.next()) {
            AverageAcademicPerformance averageAcademicPerformance = new AverageAcademicPerformance();
            averageAcademicPerformance.setLearningYear(resultSet.getString("learning_year"));
            averageAcademicPerformance.setEducationLevel(resultSet.getString("education_level"));
            averageAcademicPerformance.setSubjectName(resultSet.getString("marked_subject_name"));
            averageAcademicPerformance.setAverageMark(resultSet.getDouble("mark_weight_avg"));
            averageAcademicPerformance.setAverageYearMark(resultSet.getDouble("final_mark"));
            models.add(averageAcademicPerformance);
        }

        Set<String> years = models.stream().map(AverageAcademicPerformance::getLearningYear).collect(Collectors.toSet());
        years.forEach(y -> {
            AverageAcademicPerformanceDTO.PerformanceYear year = new AverageAcademicPerformanceDTO.PerformanceYear();
            year.setLearningYear(y);
            year.setEducationLevel(models.stream().filter(x -> x.getLearningYear().equals(y)).iterator().next().getEducationLevel());
            year.setAverageAllMarks(Precision.round(models.stream()
                    .filter(x -> x.getLearningYear().equals(y))
                    .filter(x -> Objects.nonNull(x.getAverageMark()))
                    .filter(x -> !x.getAverageMark().equals(0.0))
                    .mapToDouble(AverageAcademicPerformance::getAverageMark).average().orElse(0.0), 2));
            year.setAverageAllYearMarks(Precision.round(models.stream()
                    .filter(x -> x.getLearningYear().equals(y))
                    .filter(x -> Objects.nonNull(x.getAverageYearMark()))
                    .filter(x -> !x.getAverageYearMark().equals(0.0))
                    .mapToDouble(AverageAcademicPerformance::getAverageYearMark).average().orElse(0.0), 2));
            Set<String> subjects = models.stream()
                    .filter(x -> x.getLearningYear().equals(y)).map(AverageAcademicPerformance::getSubjectName).collect(Collectors.toSet());
            subjects.forEach(s -> {
                AverageAcademicPerformanceDTO.Subjects subject = new AverageAcademicPerformanceDTO.Subjects();
                subject.setSubjectName(s);
                subject.setAverageMark(Precision.round(models.stream()
                        .filter(x -> x.getLearningYear().equals(y))
                        .filter(x -> x.getSubjectName().equals(s)).iterator().next().getAverageMark(), 2));
                subject.setAverageYearMark(Precision.round(models.stream()
                        .filter(x -> x.getLearningYear().equals(y))
                        .filter(x -> x.getSubjectName().equals(s)).iterator().next().getAverageYearMark(), 2));
                year.getSubjects().add(subject);
                year.getSubjects().sort(Comparator.comparing(AverageAcademicPerformanceDTO.Subjects::getSubjectName));
            });

            resultDTO.getYear().add(year);
            resultDTO.getYear().sort(Comparator.comparing(AverageAcademicPerformanceDTO.PerformanceYear::getLearningYear));
        });
        return resultDTO;
    }

    @SneakyThrows
    public static List<ProgressDTO> progressMap(ResultSet resultSet) {
        List<ProgressDTO> progressDTOS = new LinkedList<>();

        while (resultSet.next()) {
            ProgressDTO progressDTO = new ProgressDTO();
            progressDTO.setSubjectId(resultSet.getInt("marked_subject_id"));
            progressDTO.setSubjectName(resultSet.getString("marked_subject_name"));
            progressDTO.setValue5(resultSet.getInt("mark_value_5"));
            progressDTO.setValue100(resultSet.getInt("mark_value_100"));
            progressDTO.setWeight(resultSet.getInt("mark_weight"));
            try {
                progressDTO.setSchoolId(resultSet.getInt("school_id"));
            } catch (Exception ex) {
                //ignore
            }
            progressDTO.setEventDate(resultSet.getDate("event_date").toLocalDate());
            progressDTO.setMarkedSubjectId(resultSet.getInt("marked_subject_id"));
            progressDTO.setMarkedSubjectName(resultSet.getString("marked_subject_name"));
            progressDTO.setAbsenceSubjectId(resultSet.getInt("lesson_absence_subject_id"));
            progressDTO.setMarkedSubjectName(resultSet.getString("lesson_absence_subject_name"));
            progressDTO.setSubjectId(resultSet.getInt("lesson_subject_id"));
            progressDTO.setSubjectName(resultSet.getString("lesson_subject_name"));
            progressDTO.setProfile(resultSet.getString("learning_profile"));
            progressDTOS.add(progressDTO);
        }

        return progressDTOS;
    }

    @SneakyThrows
    public static List<OlympiadDTO.OlympiadClickDTO> olympiadMap(ResultSet resultSet) {
        List<OlympiadDTO.OlympiadClickDTO> olympiadDTOS = new LinkedList<>();
        ZoneId zone = ZoneId.of("GMT");

        while (resultSet.next()) {
            OlympiadDTO.OlympiadClickDTO olympiadDTO = new OlympiadDTO.OlympiadClickDTO();
            olympiadDTO.setDate(resultSet.getDate("event_date").toLocalDate());
            olympiadDTO.setName(resultSet.getString("olimpiad_name"));
            olympiadDTO.setStatus(resultSet.getString("olimpiad_result"));
            olympiadDTO.setSubjects(resultSet.getString("olimpiad_subject_ids"));
            olympiadDTO.setSubjectNames(resultSet.getString("olimpiad_subject_names"));
            olympiadDTO.setType(resultSet.getString("olimpiad_type"));
            olympiadDTO.setLevel(resultSet.getString("olimpiad_level"));
            olympiadDTO.setParallels(resultSet.getString("olimpiad_available_parallels"));
            olympiadDTO.setFormat(resultSet.getString("olimpiad_format"));
            olympiadDTO.setOrganizators(resultSet.getString("olimpiad_organizators"));
            Optional.ofNullable(resultSet.getTimestamp("event_time"))
                    .ifPresent(x -> olympiadDTO.setEventTime(x.toInstant().atZone(zone).toLocalDateTime()));
            olympiadDTO.setRecordId(resultSet.getObject("record_id", UUID.class));
            olympiadDTO.setMark(resultSet.getString("olimpiad_mark"));
            olympiadDTO.setProfile(resultSet.getString("olimpiad_profile"));
            olympiadDTOS.add(olympiadDTO);
        }

        return olympiadDTOS;
    }

    @SneakyThrows
    public static List<AchievementClickDTO> achievementMap(ResultSet resultSet) {
        List<AchievementClickDTO> achievements = new LinkedList<>();
        ZoneId zone = ZoneId.of("GMT");

        while (resultSet.next()) {
            AchievementClickDTO achievementClickDTO = new AchievementClickDTO();
            achievementClickDTO.setName(resultSet.getString("achievement_name"));
            achievementClickDTO.setEventDate(resultSet.getDate("event_date").toLocalDate());

            Date achievement_completion_date = resultSet.getDate("achievement_activity_completion_date");
            achievementClickDTO.setActivityDate(Objects.isNull(achievement_completion_date) ?
                    null : achievement_completion_date.toLocalDate());

            achievementClickDTO.setType(resultSet.getString("achievement_type"));
            achievementClickDTO.setSource(resultSet.getString("achievement_source"));
            achievementClickDTO.setCategory(resultSet.getString("achievement_category"));
            achievementClickDTO.setDescription(resultSet.getString("achievement_description"));
            achievementClickDTO.setFormat(resultSet.getString("achievement_activity_format"));
            Optional.ofNullable(resultSet.getTimestamp("event_time"))
                    .ifPresent(x -> achievementClickDTO.setEventTime(x.toInstant().atZone(zone).toLocalDateTime()));
            achievementClickDTO.setRecordId(resultSet.getObject("record_id", UUID.class));
            achievements.add(achievementClickDTO);
        }

        return achievements;
    }

    @SneakyThrows
    public static List<CulturalInstitutionClickDTO> culturalMap(ResultSet resultSet) {
        List<CulturalInstitutionClickDTO> cultural = new LinkedList<>();
        ZoneId zone = ZoneId.of("GMT");

        while (resultSet.next()) {
            CulturalInstitutionClickDTO culturalDto = new CulturalInstitutionClickDTO();
            culturalDto.setRecordId(resultSet.getString("record_id"));
            culturalDto.setPersonId(resultSet.getString("person_id"));
            culturalDto.setEventType(resultSet.getString("event_type"));
            culturalDto.setPersonType(resultSet.getString("person_type"));
            culturalDto.setSchoolId(resultSet.getInt("school_id"));
            Date eventDate = resultSet.getDate("event_date");
            culturalDto.setVisitDate(Objects.isNull(eventDate) ?
                    null : eventDate.toLocalDate());
            Optional.ofNullable(resultSet.getTimestamp("event_time"))
                    .ifPresent(x -> culturalDto.setVisitTime(x.toInstant().atZone(zone).toLocalDateTime()));
            culturalDto.setCulturalInstitutionName(resultSet.getString("cultural_institution_name"));
            culturalDto.setCulturalInstitutionType(resultSet.getString("cultural_institution_type"));

            cultural.add(culturalDto);
        }

        return cultural;
    }

//    @SneakyThrows
//    public static List<LearningDTO> learningMap(ResultSet resultSet) {
//        List<LearningDTO> lessons = new LinkedList<>();
//
//        while (resultSet.next()) {
//            LearningDTO lesson = new LearningDTO();
//
//            lesson.setLessonLearningYear(resultSet.getString("lesson_learning_year"));
//            lesson.setLessonTheme(resultSet.getString("lesson_wireframe_theme"));
//            lesson.setLessonId(resultSet.getLong("lesson_id"));
//            lesson.setMarkValue5(resultSet.getInt("mark_value_5"));
//            lesson.setMarkWeight(resultSet.getInt("mark_value_100"));
//            lesson.setLessonAttendingAttribute(resultSet.getString("lesson_attending_attribute"));
//            lessons.add(lesson);
//        }
//
//        return lessons;
//    }

    @SneakyThrows
    public static List<LearningDTO> learningMapMAT(ResultSet resultSet) {
        List<LearningDTO> lessons = new LinkedList<>();
        while (resultSet.next()) {
            LearningDTO lesson = new LearningDTO();

            lesson.setLessonLearningYear(resultSet.getString("learning_year"));
            lesson.setLessonTheme(resultSet.getString("lesson_theme"));
            Array lessonIds = resultSet.getArray("lesson_id");
            for (long l : (long[]) lessonIds.getArray()) {
                lesson.getLessonId().add(l);
            }
            lesson.setLearningLessonNumber(resultSet.getInt("learning_lesson_number"));
            lesson.setPassedLearningLessonNumber(resultSet.getInt("passed_learning_lesson_number"));
            lesson.setLearningLessonNumberEmptyTheme(resultSet.getInt("learning_lesson_number_empty_theme"));
            lesson.setPassedLearningLessonNumberEmptyTheme(resultSet.getInt("passed_learning_lesson_number_empty_theme"));
            lessons.add(lesson);
        }

        return lessons;
    }

    @SneakyThrows
    public static List<LessonDTO> lessonsMap(ResultSet resultSet) {
        List<LessonDTO> lessons = new LinkedList<>();

        while (resultSet.next()) {
            LessonDTO lesson = new LessonDTO();

            lesson.setMarketLessonId(resultSet.getLong("marked_lesson_id"));
            lesson.setMarkValue5(resultSet.getInt("mark_value_5"));
            lesson.setMarkWeight(resultSet.getInt("mark_weight"));

            lessons.add(lesson);
        }

        return lessons;
    }


    @SneakyThrows
    public static List<SelfDiagnosticClickDTO> diagnosticMap(ResultSet resultSet) {
        List<SelfDiagnosticClickDTO> diagnosticClickDTOS = new ArrayList<>();

        while (resultSet.next()) {
            SelfDiagnosticClickDTO diagnosticDTO = new SelfDiagnosticClickDTO();
            diagnosticDTO.setWorkId(resultSet.getInt("self_diagnostic_work_id"));
            diagnosticDTO.setWorkName(resultSet.getString("self_diagnostic_work_name"));
            diagnosticDTO.setWorkType(resultSet.getString("self_diagnostic_work_type"));
            diagnosticDTO.setSubjectId(resultSet.getInt("self_diagnostic_subject_id"));
            diagnosticDTO.setSubjectName(resultSet.getString("self_diagnostic_subject_name"));
            diagnosticDTO.setParallel(resultSet.getInt("self_diagnostic_parallel"));
            diagnosticDTO.setAttemptNumber(resultSet.getInt("self_diagnostic_attempt_number"));
            diagnosticDTO.setFactExecutionDuration(resultSet.getObject("self_diagnostic_fact_execution_duration"));
            diagnosticDTO.setPlanExecutionDuration(resultSet.getInt("self_diagnostic_plan_execution_duration"));
            diagnosticDTO.setBaseTaskEarnedScoreCount(resultSet.getInt("self_diagnostic_base_task_earned_score_count"));
            diagnosticDTO.setProfileTaskEarnedScoreCount(resultSet.getInt("self_diagnostic_profile_task_earned_score_count"));
            diagnosticDTO.setWorkCompetition(resultSet.getInt("self_diagnostic_work_completion"));
            diagnosticDTO.setPercentExecutionResult(resultSet.getDouble("self_diagnostic_percent_execution_result"));
            diagnosticDTO.setEventDate(resultSet.getDate("event_date").toLocalDate());
            diagnosticClickDTOS.add(diagnosticDTO);
        }

        return diagnosticClickDTOS;
    }

    @SneakyThrows
    public static List<IndependentDiagnosticDTO> independentDiagnosticMap(ResultSet resultSet) {
        List<IndependentDiagnosticDTO> independentDiagnosticDTOS = new ArrayList<>();

        while (resultSet.next()) {
            IndependentDiagnosticDTO diagnosticDTO = new IndependentDiagnosticDTO();
            diagnosticDTO.setSubject(resultSet.getString("self_diagnostic_subject_name"));
            diagnosticDTO.setSchoolId(resultSet.getString("school_id"));
            diagnosticDTO.setName(resultSet.getString("self_diagnostic_work_name"));
            diagnosticDTO.setMaxResult(resultSet.getInt("self_diagnostic_max_execution_result"));
            diagnosticDTO.setResultValue(resultSet.getInt("self_diagnostic_execution_result_mark_value"));
            diagnosticDTO.setPercentResult(resultSet.getDouble("self_diagnostic_percent_execution_result"));
            diagnosticDTO.setLevelType(resolveLevelType(resultSet));
            diagnosticDTO.setEventDate(resultSet.getDate("event_date").toLocalDate());
            diagnosticDTO.setPersonId(resultSet.getString("person_id"));
            diagnosticDTO.setRecordId(resultSet.getString("record_id"));
            diagnosticDTO.setNote(resultSet.getString("note"));
            diagnosticDTO.setWorkId(resultSet.getInt("self_diagnostic_work_id"));
            diagnosticDTO.setMarkValue5(resolveMarkValue5(resultSet));
            independentDiagnosticDTOS.add(diagnosticDTO);
        }

        return independentDiagnosticDTOS;
    }

    // Определение levelType по оценке в поле markValue5
    private static String resolveLevelType(ResultSet resultSet) throws SQLException {
        String levelType = resultSet.getString("self_diagnostic_level_type");

        if (!StringUtil.isEmpty(levelType)) {
            return levelType;
        }

        // Определяем уровень по markValue5, если levelType не задан
        Short markValue5 = resolveMarkValue5(resultSet);
        if (markValue5 != null) {
            switch (markValue5) {
                case 1:
                case 2:
                    return "Ниже базового";
                case 3:
                    return "Базовый";
                case 4:
                    return "Повышенный";
                case 5:
                    return "Высокий";
                default:
                    return "";
            }
        }

        return "";
    }

    private static Short resolveMarkValue5(ResultSet resultSet) throws SQLException {
        return !StringUtil.isEmpty(resultSet.getString("mark_value_5"))
                ? resultSet.getShort("mark_value_5")
                : null;
    }

    @SneakyThrows
    public static List<ErrorEntityDTO> errorEntityMap(ResultSet resultSet) {
        return errorEntityMap(resultSet, false);
    }

    @SneakyThrows
    public static List<ErrorEntityDTO> errorEntityMap(ResultSet resultSet, Boolean isProftech) {
        List<ErrorEntityDTO> dtos = new ArrayList<>();

        while (resultSet.next()) {
            ErrorEntityDTO dto = new ErrorEntityDTO();
            dto.setEventType(resultSet.getString("event_type"));
            dto.setRecordId(resultSet.getString("record_id"));
            dto.setEventTime(resultSet.getTime("event_time").toLocalTime());
            dto.setEventDate(resultSet.getDate("event_date").toLocalDate());
            if (!isProftech) {
                dto.setAchievementCategory(resultSet.getString("achievement_category"));
                dto.setOlympiadName(resultSet.getString("olimpiad_name"));
                dto.setAchievementName(resultSet.getString("achievement_name"));
                dto.setCulturalInstitutionName(resultSet.getString("cultural_institution_name"));
                dto.setPersonId(resultSet.getString("person_id"));
            }
            dtos.add(dto);
        }
        return dtos;
    }

    @SneakyThrows
    public static List<NotificationDiagnosticDTO> diagnosticDTO(ResultSet resultSet) {
        List<NotificationDiagnosticDTO> dtos = new ArrayList<>();
        ZoneId zone = ZoneId.of("GMT");
        while (resultSet.next()) {
            NotificationDiagnosticDTO dto = new NotificationDiagnosticDTO();

            dto.setPersonId(resultSet.getString("person_id"));
            dto.setRecordId(resultSet.getString("record_id"));
            Optional.ofNullable(resultSet.getTimestamp("creation_date"))
                    .ifPresent(x -> dto.setCreatedDate(x.toInstant().atZone(zone).toLocalDateTime()));
            dto.setNote(resultSet.getString("note"));
            dto.setEventType(resultSet.getString("event_type"));
            dtos.add(dto);
        }

        return dtos;
    }

    @SneakyThrows
    public static List<CertificatesDTO.Certificate> certificateMap(ResultSet resultSet) {
        List<CertificatesDTO.Certificate> dtos = new ArrayList<>();
        ZoneId zone = ZoneId.of("GMT");

        while (resultSet.next()) {
            CertificatesDTO.Certificate dto = new CertificatesDTO.Certificate();
            dto.setType(resultSet.getString("additional_education_certificates_type"));
            dto.setNumber(resultSet.getString("additional_education_certificates_number"));
            dto.setOrganization(resultSet.getString("additional_education_certificates_organization"));
            dto.setOrganizationId(resultSet.getLong("additional_education_certificates_organization_id"));
            dto.setProfession(resultSet.getString("additional_education_certificates_profession"));
            dto.setProgramm(resultSet.getString("additional_education_certificates_programm"));
            dto.setQualification(resultSet.getString("additional_education_certificates_qualification"));
            Optional.ofNullable(resultSet.getTimestamp("event_time"))
                    .ifPresent(x -> dto.setEventTime(x.toInstant().atZone(zone).toLocalDateTime()));
            dto.setEventDate(resultSet.getDate("event_date").toLocalDate());
            dtos.add(dto);
        }

        return dtos;
    }

    @SneakyThrows
    public static List<Pair<String, String>> olympiadForGratitude(ResultSet resultSet) {
        List<Pair<String, String>> pairs = new ArrayList<>();
        while (resultSet.next()) {
            Pair<String, String> pair = Pair.of(resultSet.getString("olimpiad_name"),
                    resultSet.getString("olimpiad_result"));
            pairs.add(pair);
        }
        return pairs;
    }

    @SneakyThrows
    public static List<IndependentDiagnosticRatingDTO> independentDiagnosticRating(ResultSet resultSet, Boolean isSchool) {
        List<IndependentDiagnosticRatingDTO> result = new ArrayList<>();
        while (resultSet.next()) {
            IndependentDiagnosticRatingDTO dto = new IndependentDiagnosticRatingDTO();
            dto.setDiagnosticWorkId(resultSet.getInt("diagnostic_work_id"));
            dto.setDiagnosticSubjectName(resultSet.getString("diagnostic_subject_name"));
            dto.setDiagnosticMaxExecutionResult(resultSet.getInt("diagnostic_max_execution_result"));
            dto.setLearningYear(resultSet.getString("learning_year"));
            dto.setFirstPlaceScore(resultSet.getInt("first_place_scores"));
            dto.setFirstPlaceResultPercent(resultSet.getDouble("first_place_result_percent"));
            dto.setFirstPlaceStudentCount(resultSet.getInt("first_place_student_count"));
            dto.setFirstPlaceLevelType(resultSet.getString("first_place_level_type"));
            List<Integer> list = new ArrayList<>();
            Array scores = resultSet.getArray("other_place_score");
            for (int l : (int[]) scores.getArray()) {
                list.add(l);
            }
            list.sort(Comparator.reverseOrder());
            dto.setOtherPlaceScore(list);
            dto.setOtherPlaceResultPercent(resultSet.getString("other_place_result_percent"));
            dto.setPlaceStudentCount(resultSet.getString("place_student_count"));
            dto.setLastPlaceStudentNumber(resultSet.getInt("last_place_student_number"));
            dto.setLastPlaceScore(resultSet.getInt("last_place_scores"));
            dto.setLastPlaceResultPercent(resultSet.getDouble("last_place_result_percent"));
            dto.setLastPlaceStudentCount(resultSet.getInt("last_place_student_counts"));
            dto.setLastPlaceLevelType(resultSet.getString("last_place_level_type"));
            if (isSchool) dto.setSchoolId(resultSet.getString("school_id"));
            result.add(dto);

        }
        return result;
    }

    @SneakyThrows
    public static List<IndependentDiagnosticLevelDTO> independentDiagnosticLevel(ResultSet resultSet, Boolean isSchool) {
        List<IndependentDiagnosticLevelDTO> result = new ArrayList<>();
        while (resultSet.next()) {
            IndependentDiagnosticLevelDTO dto = new IndependentDiagnosticLevelDTO();
            dto.setDiagnosticWorkId(resultSet.getInt("diagnostic_work_id"));
            dto.setDiagnosticSubjectName(resultSet.getString("diagnostic_subject_name"));
            dto.setDiagnosticExecutionResultMarkValue(resultSet.getInt("diagnostic_execution_result_mark_value"));
            dto.setLearningYear(resultSet.getString("learning_year"));
            dto.setDiagnosticLevelType(resultSet.getString("diagnostic_level_type"));
            dto.setDiagnosticMaxExecutionResult(resultSet.getInt("diagnostic_max_execution_result"));
            if (isSchool) dto.setSchoolId(resultSet.getString("school_id"));
            result.add(dto);
        }
        return result;
    }

    @SneakyThrows
    public static List<IndependentDiagnosticByPersonDTO> independentDiagnosticByPerson(ResultSet resultSet, Boolean isSchool) {
        List<IndependentDiagnosticByPersonDTO> result = new ArrayList<>();
        while (resultSet.next()) {
            IndependentDiagnosticByPersonDTO dto = new IndependentDiagnosticByPersonDTO();
            dto.setPersonId(resultSet.getString("person_id"));
            dto.setDiagnosticWorkId(resultSet.getInt("diagnostic_work_id"));
            dto.setDiagnosticSubjectName(resultSet.getString("diagnostic_subject_name"));
            dto.setDiagnosticExecutionResultMarkValue(resultSet.getInt("diagnostic_execution_result_mark_value"));
            dto.setDiagnosticPercentExecutionResult(resultSet.getDouble("diagnostic_percent_execution_result"));
            dto.setLearningYear(resultSet.getString("learning_year"));
            dto.setDiagnosticMaxExecutionResult(resultSet.getInt("diagnostic_max_execution_result"));
            if (isSchool) dto.setSchoolId(resultSet.getString("school_id"));
            result.add(dto);
        }
        return result;
    }

    @SneakyThrows
    public static List<DiagnosticDTO> diagnostic(ResultSet resultSet) {
        List<DiagnosticDTO> result = new ArrayList<>();
        while (resultSet.next()) {
            DiagnosticDTO dto = new DiagnosticDTO();
            dto.setPersonId(resultSet.getString("person_id"));
            dto.setDiagnosticWorkId(resultSet.getInt("self_diagnostic_work_id"));
            dto.setDiagnosticSubjectName(resultSet.getString("self_diagnostic_subject_name"));
            dto.setDiagnosticMaxExecutionResult(resultSet.getInt("self_diagnostic_max_execution_result"));
            dto.setSchoolId(resultSet.getString("school_id"));
            dto.setMarkValue5(resultSet.getString("mark_value_5") == null ? null : resultSet.getShort("mark_value_5"));
            Date eventDate = resultSet.getDate("event_date");
            if (Objects.nonNull(eventDate)) {
                LocalDate localDate = eventDate.toLocalDate();
                if (localDate.getMonth().getValue() >= 9) {
                    dto.setLearningYear(localDate.getYear() + "-" + (localDate.getYear() + 1));
                } else {
                    dto.setLearningYear((localDate.getYear() - 1) + "-" + (localDate.getYear()));
                }

            }
            result.add(dto);
        }
        return result;
    }

    @SneakyThrows
    public static String diagnosticSchool(ResultSet resultSet) {
        String schoolId = null;
        while (resultSet.next()) {
            schoolId = resultSet.getString("school_id");
        }
        return schoolId;
    }

    @SneakyThrows
    public static List<GeneralDiagnosticRatingDTO> mapGeneralDiagnostic(ResultSet resultSet, Boolean bySchool) {
        List<GeneralDiagnosticRatingDTO> result = new ArrayList<>();
        while (resultSet.next()) {
            GeneralDiagnosticRatingDTO dto = new GeneralDiagnosticRatingDTO();
            dto.setPersonId(resultSet.getString("person_id"));
            if (bySchool) dto.setSchoolId(resultSet.getString("school_id"));
            dto.setDiagnosticSubjectName(resultSet.getString("diagnostic_subject_name"));
            dto.setLearningYear(resultSet.getString("learning_year"));
            dto.setAveragePercentExecutionResult(resultSet.getDouble("average_result"));
            result.add(dto);
        }
        return result;
    }

    @SneakyThrows
    public static List<GeneralRatingDistinctInfo> mapGeneralDistinctAveragePercent(ResultSet resultSet) {
        List<GeneralRatingDistinctInfo> result = new ArrayList<>();
        while (resultSet.next()) {
            GeneralRatingDistinctInfo dto = new GeneralRatingDistinctInfo();
            List<Double> list = new ArrayList<>();
            Array lessonIds = resultSet.getArray("distinct_average_result");
            for (double l : (double[]) lessonIds.getArray()) {
                list.add(l);
            }
            list.sort(Comparator.reverseOrder());
            dto.setDiagnosticSubjectName(resultSet.getString("diagnostic_subject_name"));
            dto.setLearningYear(resultSet.getString("learning_year"));
            dto.setDistinctPercentResult(list);

            result.add(dto);
        }
        return result;
    }

    @SneakyThrows
    public static List<CountPercentsBySubjects> mapCountLowerPercents(ResultSet resultSet) {
        List<CountPercentsBySubjects> result = new ArrayList<>();
        while (resultSet.next()) {
            CountPercentsBySubjects dto = new CountPercentsBySubjects();
            dto.setDiagnosticSubjectName(resultSet.getString("diagnostic_subject_name"));
            dto.setLearningYear(resultSet.getString("learning_year"));
            dto.setCountPercents(resultSet.getDouble("count_lower"));

            result.add(dto);
        }
        return result;
    }

    @SneakyThrows
    public static List<CountPercentsBySubjects> mapCountAllPercents(ResultSet resultSet) {
        List<CountPercentsBySubjects> result = new ArrayList<>();
        while (resultSet.next()) {
            CountPercentsBySubjects dto = new CountPercentsBySubjects();
            dto.setDiagnosticSubjectName(resultSet.getString("diagnostic_subject_name"));
            dto.setLearningYear(resultSet.getString("learning_year"));
            dto.setCountPercents(resultSet.getDouble("count_all"));

            result.add(dto);
        }
        return result;
    }

    @SneakyThrows
    public static List<ProftechDTO> mapProftechData(ResultSet resultSet) {
        List<ProftechDTO> result = new ArrayList<>();
        ZoneId zone = ZoneId.of("GMT");

        while (resultSet.next()) {
            ProftechDTO dto = new ProftechDTO();
            dto.setPersonId(resultSet.getString("person_id"));
            dto.setRecordId(resultSet.getString("record_id"));
            Optional.ofNullable(resultSet.getTimestamp("creation_date"))
                    .ifPresent(x -> dto.setCreationDate(x.toInstant().atZone(zone).toLocalDateTime()));
            dto.setEventId(resultSet.getString("event_id"));
            dto.setPersonType(resultSet.getString("person_type"));
            Optional.ofNullable(resultSet.getDate("event_date"))
                    .ifPresent(x -> dto.setEventDate(x.toLocalDate()));
            Optional.ofNullable(resultSet.getTimestamp("change_datetime"))
                    .ifPresent(x -> dto.setChangeDateTime(x.toInstant().atZone(zone).toLocalDateTime()));
            dto.setEventType(resultSet.getString("event_type"));
            Optional.ofNullable(resultSet.getTimestamp("event_time"))
                    .ifPresent(x -> dto.setEventTime(x.toInstant().atZone(zone).toLocalDateTime()));
            dto.setSchoolId(resultSet.getString("school_id"));
            dto.setSchoolName(resultSet.getString("school"));
            dto.setDistrict(resultSet.getString("district"));
            dto.setOkrug(resultSet.getString("okrug"));
            dto.setRegionName(resultSet.getString("region_name"));
            dto.setCountry(resultSet.getString("country"));
            dto.setProfessionName(resultSet.getString("profession_name"));
            dto.setEducationLevel(resultSet.getString("education_level"));
            dto.setLearningYear(resultSet.getString("learning_year"));
            dto.setStudyForm(resultSet.getString("study_form"));
            dto.setStudyTerm(resultSet.getString("study_term"));
            dto.setProgramBase(resultSet.getString("program_base"));
            dto.setIsAdvancedLevel(getNullableBoolean(resultSet.getString("is_advanced_level")));
            Optional.ofNullable(resultSet.getString("education_course"))
                    .ifPresent(x -> dto.setEducationCourse(Integer.valueOf(x)));
            Optional.ofNullable(resultSet.getDate("study_start_date"))
                    .ifPresent(x -> dto.setStudyStartDate(x.toLocalDate()));
            dto.setGroupName(resultSet.getString("group_name"));
            dto.setIsSmallGroup(getNullableBoolean(resultSet.getString("is_small_group")));
            dto.setIsOutletGroup(getNullableBoolean(resultSet.getString("is_outlet_group")));
            dto.setEducationName(resultSet.getString("education_name"));
            dto.setEStudyTerm(resultSet.getString("e_study_term"));
            dto.setEGroupName(resultSet.getString("e_group_name"));
            dto.setEIsSmallGroup(getNullableBoolean(resultSet.getString("e_is_small_group")));
            dto.setEIsOutletGroup(getNullableBoolean(resultSet.getString("e_is_outlet_group")));
            dto.setIsDocWithPracticeOrg(getNullableBoolean(resultSet.getString("is_doc_with_practice_org")));
            Optional.ofNullable(resultSet.getTimestamp("doc_issue_date"))
                    .ifPresent(x -> dto.setDocIssueDate(x.toInstant().atZone(zone).toLocalDateTime()));
            dto.setDocNum(resultSet.getString("doc_num"));
            dto.setEmploymentType(resultSet.getString("employment_type"));
            dto.setLevelBusiness(resultSet.getString("level_business"));
            dto.setSalaryRange(resultSet.getString("salary_range"));
            dto.setIsByProfile(getNullableBoolean(resultSet.getString("is_by_profile")));
            dto.setJobStudyPlace(resultSet.getString("job_study_place"));
            dto.setIsDocForEmployment(getNullableBoolean(resultSet.getString("is_doc_for_employment")));
            dto.setDocType(resultSet.getString("doc_type"));
            dto.setOrganization(resultSet.getString("organization"));
            Optional.ofNullable(resultSet.getDate("doc_employment_date"))
                    .ifPresent(x -> dto.setDocEmploymentDate(x.toLocalDate()));
            dto.setStatus(resultSet.getString("status"));
            Optional.ofNullable(resultSet.getDate("expel_certify_date"))
                    .ifPresent(x -> dto.setExpelCertifyDate(x.toLocalDate()));
            dto.setCertDoc(resultSet.getString("cert_doc"));
            dto.setCertDocSeries(resultSet.getString("cert_doc_series"));
            dto.setCertDocNum(resultSet.getString("cert_doc_num"));

            result.add(dto);
        }
        return result;
    }

    private static Boolean getNullableBoolean(String value) {
        if (value == null) {
            return null;
        }
        if (value.equals("1")) {
            return true;
        } else {
            return false;
        }
    }

    @SneakyThrows
    public static List<ProftechJobDTO> mapProftechJobData(ResultSet resultSet) {
        List<ProftechJobDTO> result = new ArrayList<>();
        ZoneId zone = ZoneId.of("GMT");

        while (resultSet.next()) {
            ProftechJobDTO dto = new ProftechJobDTO();
            dto.setPersonId(resultSet.getString("person_id"));
            dto.setRecordId(resultSet.getString("record_id"));
            Optional.ofNullable(resultSet.getTimestamp("creation_date"))
                    .ifPresent(x -> dto.setCreationDate(x.toInstant().atZone(zone).toLocalDateTime()));
            Optional.ofNullable(resultSet.getTimestamp("change_datetime"))
                    .ifPresent(x -> dto.setChangeDateTime(x.toInstant().atZone(zone).toLocalDateTime()));
            dto.setEventId(resultSet.getString("event_id"));
            dto.setEmploymentTypeId(resultSet.getInt("employment_type_id"));
            dto.setEmploymentType(resultSet.getString("employment_type"));
            dto.setLevelBusinessId(resultSet.getInt("level_business_id"));
            dto.setLevelBusiness(resultSet.getString("level_business"));
            dto.setSalaryRangeId(resultSet.getInt("salary_range_id"));
            dto.setSalaryRange(resultSet.getString("salary_range"));
            dto.setIsByProfile(resultSet.getBoolean("is_by_profile"));
            dto.setJobStudyPlace(resultSet.getString("job_study_place"));
            dto.setIsDocForEmployment(resultSet.getBoolean("is_doc_for_employment"));
            dto.setDocTypeId(resultSet.getInt("doc_type_id"));
            dto.setDocType(resultSet.getString("doc_type"));
            dto.setOrganization(resultSet.getString("organization"));
            Optional.ofNullable(resultSet.getDate("doc_employment_date"))
                    .ifPresent(x -> dto.setDocEmploymentDate(x.toLocalDate()));


            result.add(dto);
        }
        return result;
    }

    @SneakyThrows
    public static List<PersonalDiagnosticDTO> mapPersonalDiagnostic(ResultSet resultSet) {
        List<PersonalDiagnosticDTO> diagnostics = new ArrayList<PersonalDiagnosticDTO>();
        while (resultSet.next()) {
            PersonalDiagnosticDTO dto = new PersonalDiagnosticDTO();
            dto.setRecordId(resultSet.getString("record_id"));
            dto.setPersonId(resultSet.getString("person_id"));
            dto.setWorkId(resultSet.getLong("self_diagnostic_work_id"));
            dto.setSubject(resultSet.getString("self_diagnostic_subject_name"));
            dto.setName(resultSet.getString("self_diagnostic_work_name"));
            dto.setResultValue(resultSet.getShort("self_diagnostic_execution_result_mark_value"));
            dto.setPercentResult(resultSet.getDouble("self_diagnostic_percent_execution_result"));
            dto.setEventDate(resultSet.getDate("event_date").toLocalDate());

            diagnostics.add(dto);
        }

        return diagnostics;
    }
}
