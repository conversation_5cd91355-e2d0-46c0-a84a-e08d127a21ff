package ru.portfolio.ax.util;

import com.google.common.collect.Iterables;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.lang.Nullable;
import ru.portfolio.ax.model.common.AbstractEntity;
import ru.portfolio.ax.model.common.AbstractRefEntity;
import ru.portfolio.ax.model.common.RefEntity;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.service.CrudService;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.Temporal;
import java.util.*;
import java.util.function.*;
import java.util.regex.Pattern;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.google.common.base.CaseFormat.LOWER_CAMEL;
import static com.google.common.base.CaseFormat.LOWER_UNDERSCORE;
import static java.util.Collections.emptySet;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.apache.commons.lang3.StringUtils.SPACE;

public abstract class Utils {
    public static boolean isSimple(Class<?> clazz) {
        if (AbstractEntity.class.isAssignableFrom(clazz)) {
            return false;
        }
        if (Map.class.isAssignableFrom(clazz)) {
            return true;
        }
        if (BeanUtils.isSimpleProperty(clazz)) {
            return true;
        }
        if (Temporal.class.isAssignableFrom(clazz)) {
            return true;
        }
        return false;
    }


    public static <E> Predicate<E> not(Predicate<E> predicate) {
        return predicate.negate();
    }

    public static Set<String> split(String ids) {
        return Stream.of(StringUtils.split(StringUtils.defaultString(ids), ','))
                .map(String::trim).collect(Collectors.toSet());
    }

    @Nullable
    public static String join(Collection<? extends Object> source) {
        return join(source, String::valueOf, ",");
    }

    public static <T> String join(Collection<T> source, Function<T, String> extractor) {
        return join(source, extractor, ",");
    }

    public static <T> String join(Collection<T> source, Function<T, String> extractor, String delimiter) {
        if (CollectionUtils.isEmpty(source)) return null;
        return source.stream().filter(Objects::nonNull).map(extractor).collect(Collectors.joining(delimiter));
    }

    public static <ID extends Number> Set<ID> splitTo(String ids, Function<String, ID> adapter) {
        return Stream.of(StringUtils.split(StringUtils.defaultString(ids), ','))
                .filter(Objects::nonNull).map(String::trim).map(adapter)
                .collect(Collectors.toSet());
    }

    public static Set<Long> splitToIds(String ids) {
        return splitTo(ids, Long::valueOf);
    }

    public static <T> Consumer<T> lambdasTryCatchWrapper(Consumer<T> consumer, Logger log) {
        return entity -> {
            try {
                consumer.accept(entity);
            } catch (Exception ex) {
                log.error(ex.getMessage());
            }
        };
    }

    public static String camel2underscore(String text) {
        return safeGet(text, t -> LOWER_CAMEL.to(LOWER_UNDERSCORE, t));
    }

    @Nullable
    public static <E, V> V safeGet(@Nullable E object, Function<E, V> converter) {
        return Optional.ofNullable(object).map(converter).orElse(null);
    }

    public static <T> T safeGet(Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            return null;
        }
    }

    @Nullable
    public static <V> V safetyGet(Supplier<V> supplier) {
        return safetyGet(supplier, t -> true);
    }

    @Nullable
    public static <V> V safetyGet(V def, Supplier<V> supplier) {
        return Optional.ofNullable(safetyGet(supplier, t -> true)).orElse(def);
    }

    @Nullable
    public static <V> V safetyGet(Supplier<V> supplier, Predicate<Exception> exceptionPredicate) {
        try {
            return supplier.get();
        } catch (Exception e) {
            if (exceptionPredicate.test(e)) {
                return null;
            } else {
                throw e;
            }
        }
    }

    public static <E> void safeSet(@Nullable E object, Consumer<E> converter) {
        Optional.ofNullable(object).ifPresent(converter);
    }

    public static <K, V> Map<K, V> index(Collection<V> collection, Function<V, K> converter) {
        return index(collection, converter, Function.identity());
    }

    public static <K, V, E> Map<K, E> index(Collection<V> collection, Function<V, K> converter, Function<V, E> valueMapper) {
        return CollectionUtils.emptyIfNull(collection).stream().collect(Collectors.toMap(converter, valueMapper, (x, y) -> x));
    }

    public static <T, R> Set<R> extract(Collection<T> source, Function<T, R> extractor) {
        return extract(source, extractor, r -> true);
    }

    @Nullable
    public static <T> T first(Collection<T> source) {
        if (CollectionUtils.isEmpty(source)) return null;
        return source.iterator().next();
    }

    public static <T> T first(Collection<T> source, Predicate<T> predicate) {
        if (CollectionUtils.isEmpty(source)) return null;
        return source.stream().filter(predicate).findFirst().orElse(null);
    }

    @Nullable
    public static <T, R> R extractFirst(Collection<T> source, Function<T, R> extractor) {
        return Iterables.getFirst(extract(source, extractor, r -> true), null);
    }

    @Nullable
    public static <T, R> R extractFirst(Collection<T> source, Predicate<T> predicate, Function<T, R> extractor) {
        return safeGet(Iterables.getFirst(extract(source, Function.identity(), predicate), null), extractor);
    }

    public static String joinIn(Collection<?> source, String what, String defValue) {
        if (CollectionUtils.isEmpty(source)) return defValue;
        return source.stream().map(String::valueOf).collect(Collectors
                .joining(", ", what + " in (", ")"));
    }

    public static <T, R> List<R> transform(Collection<T> source, Function<T, R> extractor) {
        return transform(source, extractor, r -> true);
    }

    public static <T, R> List<R> transform(Collection<T> source, Function<T, R> extractor, Predicate<R> predicate) {
        return extract(source, extractor, predicate, toList());
    }

    public static <T> List<T> filter(Collection<T> source, Predicate<T> predicate) {
        return transform(source, Function.identity(), predicate);
    }

    public static <T, R> Set<R> extract(Collection<T> source, Function<T, R> extractor, Predicate<R> valuePredicate) {
        return extract(source, extractor, valuePredicate, toSet());
    }

    public static <T, R, C extends Iterable<R>> C extract(Collection<T> source, Function<T, R> extractor,
                                                          Predicate<R> valuePredicate, Collector<R, ?, C> collector) {
        return extract(source, extractor, t -> true, valuePredicate, collector);
    }

    public static <T, R, C extends Iterable<R>> C extract(Collection<T> source, Function<T, R> extractor,
                                                          Predicate<T> keyPredicate, Predicate<R> valuePredicate,
                                                          Collector<R, ?, C> collector) {
        return CollectionUtils.emptyIfNull(source).stream().filter(keyPredicate).map(extractor).filter(valuePredicate).collect(collector);
    }

    public static <T> T copyNonNullPropertiesAndGet(Object src, T target, String... ignoredFields) {
        copyNonNullProperties(src, target, ignoredFields);
        return target;
    }

    public static void copyNonNullProperties(Object src, Object target, String... ignoredFields) {
        copyProperties(src, target, emptySet(), ignoredFields);
    }

    public static void copyProperties(Object src, Object target, Set<String> mustCopyFields, String... ignoredFields) {
        if (Objects.isNull(src)) return;
        BeanWrapper wrappedSrc = new BeanWrapperImpl(src);
        PropertyDescriptor[] propertyDescriptors = wrappedSrc.getPropertyDescriptors();
        Set<String> emptyNames = Sets.newHashSet(ignoredFields);
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            Object srcValue = null;
            if (emptyNames.contains(propertyDescriptor.getName())) continue;
            try {
                srcValue = wrappedSrc.getPropertyValue(propertyDescriptor.getName());
            } catch (Exception ignored) {
            }
            if (srcValue == null) emptyNames.add(propertyDescriptor.getName());
        }
        emptyNames.removeAll(mustCopyFields);
        String[] result = new String[emptyNames.size()];
        BeanUtils.copyProperties(src, target, emptyNames.toArray(result));
    }

    public static String pageableToClickHouseOrderSql(Pageable pageable) {
        return pageable.getSort().get()
                .map(s -> StringUtils.join(s.getProperty(), SPACE, s.getDirection().name().toLowerCase()))
                .collect(Collectors.joining(", ", " order by ", StringUtils.EMPTY));
    }

    public static String pageableToClickHouseLimitSql(Pageable pageable) {
        int offset = pageable.getPageNumber() * pageable.getPageSize();
        return StringUtils.join(" limit ", pageable.getPageSize(), " offset ", offset);
    }

    public static <T> Set<T> set() {
        return new HashSet<T>() {
            @Override
            public boolean contains(Object o) {
                return true;
            }
        };
    }

    public static <R extends AbstractRefEntity<Integer>> void reachRefs2map(Map<String, Object> map, String ids,
                                                                            Class<R> refClass, CrudService crudService) {
        List<R> allRefs = crudService.findAllRefs(Utils.splitTo(ids, Integer::valueOf), refClass);
        if (CollectionUtils.isNotEmpty(allRefs)) {
            String key = refClass.getSimpleName();
            key = StringUtils.substringBefore(key, "Ref");
            key = StringUtils.appendIfMissing(key, "s");
            key = StringUtils.uncapitalize(key);
            map.put(key, allRefs);
        }
    }

    @SneakyThrows
    public static <T extends AbstractEntity> String createHash(T entity) {
        StringBuilder collect = new StringBuilder();
        Class clazz = entity.getClass();
        while (!clazz.equals(Object.class)) {
            //Переводим время в московское, что бы не зависимо от тайм зоны был одинаковый хэш
            ZoneId timeZone = ZoneId.of("Europe/Moscow");
            for (Field field : entity.getClass().getDeclaredFields()) {
                if (field.getType().isAssignableFrom(ZonedDateTime.class)) {
                    ZonedDateTime zdt = (ZonedDateTime) getFieldOfObject(field, entity);
                    if (nonNull(zdt)) {
                        timeZone = zdt.getZone();
                        field.set(entity, zdt.withZoneSameInstant(ZoneId.of("Europe/Moscow")));
                    }
                }
            }
            collect.append(Stream.of(clazz.getDeclaredFields())
                    .filter(x -> !Arrays.asList("id", "creatorId", "linkedObjectIds", "linkedObjects", "creationDate", "fileReferences",
                            "hashCode", "map", "isImport", "editDate", "editedDate", "source", "sourceCode").contains(x.getName()))
                    .map(x -> getFieldOfObject(x, entity))
                    .filter(Objects::nonNull)
                    .map(x -> {
                        if (x instanceof RefEntity) {
                            return ((RefEntity) x).getCode().toString();
                        } else {
                            return x.toString();
                        }
                    })
                    .map(Object::toString)
                    .map(String::toLowerCase)
                    .map(x -> x.replaceAll("[^A-Za-z0-9А-Яа-яЁё]", ""))
                    .collect(Collectors.joining()));
            //Возвращаем исходную тайм зону
            for (Field field : entity.getClass().getDeclaredFields()) {
                if (field.getType().isAssignableFrom(ZonedDateTime.class)) {
                    ZonedDateTime zdt = (ZonedDateTime) getFieldOfObject(field, entity);
                    if (nonNull(zdt)) {
                        field.set(entity, zdt.withZoneSameInstant(timeZone));
                    }
                }
            }
            clazz = clazz.getSuperclass();
        }
        return DigestUtils.md5Hex(collect.toString());
    }


    public static <T extends AbstractEntity> void checkForSwearInList(T entity, List<String> swears) {
        List<String> stringFields = new ArrayList<>();
        Class clazz = entity.getClass();
        while (!clazz.equals(Object.class)) {
            stringFields.addAll(Stream.of(clazz.getDeclaredFields())
                    .filter(x -> !Arrays.asList("id", "creatorId", "linkedObjectIds", "creationDate", "fileReferences",
                            "entityId", "hashCode", "map").contains(x.getName()))
                    .filter(x -> x.getType().equals(String.class))
                    .map(x -> getFieldOfObject(x, entity))
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(Collectors.toList()));
            clazz = clazz.getSuperclass();
        }
        swears.forEach(word -> {
            PortfolioException.check(stringFields.stream()
                    .filter(Objects::nonNull).map(String::toLowerCase)
                    .noneMatch(x -> x.contains(word.toLowerCase())), PortfolioException.get465());
        });
    }

    public static void checkForSwearInList(String value, List<String> swears) {
        swears.forEach(word -> {
            PortfolioException.check(!value.toLowerCase().contains(word.toLowerCase()), PortfolioException.get465());
        });
    }

    public static void checkForSwear(String value, List<String> swears) {
        swears.forEach(word -> {
            PortfolioException.check(!value.toLowerCase().contains(word.toLowerCase()), PortfolioException.get468());
        });
    }

    @SneakyThrows
    private static <T extends AbstractEntity> Object getFieldOfObject(Field field, T obj) {
        field.setAccessible(true);
        return field.get(obj);
    }

    @SneakyThrows
    public static <T, E> void fillDeclaredFieldWithValue(T obj, String fieldName, E value) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(obj, value);
        } catch (Exception ex) {
            try {
                if (obj.getClass().getSuperclass() != null) {
                    Field field = obj.getClass().getSuperclass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    field.set(obj, value);
                }
            } catch (Exception exIn) {
                // just left it null then
            }
            // just left it null then
        }
    }

    public static <T> void sortList(List<T> t, final String fieldName, String direction) {
        Comparator<T> comparator = new Comparator<T>() {
            @Override
            public int compare(T o1, T o2) {
                String field1 = getProperty(o1, fieldName).toString();
                String field2 = getProperty(o2, fieldName).toString();
                return field1.compareTo(field2);
            }
        };
        if (direction.equals("desc")) {
            t.sort(comparator.reversed());
        } else if (direction.equals("asc")) {
            t.sort(comparator);
        } else {
            throw PortfolioException.get464();
        }
    }

    private static Object getProperty(Object bean, String fieldName) {
        Field[] fields = bean.getClass().getDeclaredFields();
        Object obj = null;
        for (Field field : fields) {
            if (fieldName.equals(field.getName())) {
                Field.setAccessible(fields, true);
                try {
                    obj = field.get(bean);
                } catch (IllegalArgumentException | IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
        PortfolioException.check(nonNull(obj), PortfolioException.get464());
        return obj;
    }

    @SuppressWarnings("unchecked")
    public static <T> T cast(Object object) {
        if (Objects.isNull(object)) {
            return null;
        }
        return (T) object;
    }

    public static boolean isValidUUID(String str) {
        Pattern uuidPattern = Pattern.compile("^[{]?[0-9a-fA-F]{8}-([0-9a-fA-F]{4}-){3}[0-9a-fA-F]{12}[}]?$");
        if (str == null) {
            return false;
        }
        return uuidPattern.matcher(str).matches();
    }

    public static <T, E> T getKeyByValue(Map<T, E> map, E value) {
        return map.entrySet()
                .stream()
                .filter(entry -> Objects.equals(entry.getValue(), value))
                .map(Map.Entry::getKey)
                .findFirst().orElse(null);
    }

    public static <T, A, R> Collector<T, ?, R> filtering(
            Predicate<? super T> predicate, Collector<? super T, A, R> downstream) {

        BiConsumer<A, ? super T> accumulator = downstream.accumulator();
        return Collector.of(downstream.supplier(),
                (r, t) -> {
                    if (predicate.test(t)) accumulator.accept(r, t);
                },
                downstream.combiner(), downstream.finisher(),
                downstream.characteristics().toArray(new Collector.Characteristics[0]));
    }

    public static boolean allNull(Object target) {
        return Arrays.stream(target.getClass()
                        .getDeclaredFields())
                .peek(f -> f.setAccessible(true))
                .map(f -> getFieldValue(f, target))
                .allMatch(Objects::isNull);
    }

    private static Object getFieldValue(Field field, Object target) {
        try {
            return field.get(target);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
