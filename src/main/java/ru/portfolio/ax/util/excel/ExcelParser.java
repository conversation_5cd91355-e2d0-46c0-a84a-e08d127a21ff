package ru.portfolio.ax.util.excel;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.CastUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ru.portfolio.ax.model.*;
import ru.portfolio.ax.model.common.PersonallyEntity;
import ru.portfolio.ax.model.ref.*;
import ru.portfolio.ax.repository.ExcelCachePartitionRepository;
import ru.portfolio.ax.repository.ExcelCacheRepository;
import ru.portfolio.ax.repository.ImportHistoryRepository;
import ru.portfolio.ax.repository.ref.*;
import ru.portfolio.ax.rest.dto.AttachmentDTO;
import ru.portfolio.ax.rest.dto.AttachmentsDTO;
import ru.portfolio.ax.rest.dto.ExcelSearchParamsDTO;
import ru.portfolio.ax.rest.dto.UserContextDTO;
import ru.portfolio.ax.rest.dto.aupd.AccessTokenPayloadDto;
import ru.portfolio.ax.rest.dto.aupd.AggregatedGlobalRole;
import ru.portfolio.ax.rest.dto.contingent.PersonDTO;
import ru.portfolio.ax.rest.dto.excel.AdministrationErrorRecordRow;
import ru.portfolio.ax.rest.dto.excel.ErrorMessageFileInfo;
import ru.portfolio.ax.rest.dto.nsi.NsiDTO;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.service.CrudService;
import ru.portfolio.ax.service.ext.AUPDService;
import ru.portfolio.ax.service.ext.ContingentService;
import ru.portfolio.ax.service.ext.NsiService;
import ru.portfolio.ax.util.Utils;
import ru.portfolio.ax.util.mapper.excel.*;
import ru.portfolio.ax.util.security.AuthComponentNew;
import ru.portfolio.ax.util.security.AuthService;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.LongStream;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.concurrent.TimeUnit.HOURS;
import static ru.portfolio.ax.util.Utils.safetyGet;
import static ru.portfolio.ax.util.excel.ExcelUtils.*;

@Service
@RequiredArgsConstructor
public class ExcelParser {
    private final EventMapper eventMapper;
    private final RewardMapper rewardMapper;
    private final EmploymentMapper employmentMapper;
    private final ProjectMapper projectMapper;
    private final SportRewardMapper sportRewardMapper;
    private final AffilationMapper affilationMapper;
    private final WorldSkillsMapper worldSkillsMapper;
    private final ObjectMapper objectMapper;

    private final AUPDService aupdService;
    private final CrudService crudService;
    private final AuthService authService;
    private final NsiService nsiService;

    private final ContingentService contingentService;
    private final AuthComponentNew authComponent;

    private final ExcelCacheRepository excelCacheRepository;
    private final ExcelCachePartitionRepository excelCachePartitionRepository;
    private final ImportHistoryRepository importHistoryRepository;
    private final ActionTypeRefRepository actionTypeRefRepository;
    private final ActionKindRefRepository actionKindRefRepository;
    private final StopWordRepository stopWordRepository;
    private final TemplatesRefRepository templatesRefRepository;

    private final EventKindRefRepository eventKindRefRepository;
    private final RewardKindRefRepository rewardKindRefRepository;
    private final AffKindRefRepository affilationKindRefRepository;
    private final OlympiadTypeRepository olympiadTypeRepository;
    private final OlympiadFormatRepository olympiadFormatRepository;
    private final SportRewardRefRepository sportRewardRefRepository;
    private final SubcategoryRefRepository subcategoryRefRepository;
    private final OrganizatorRefRepository organizatorRefRepository;
    private final TrainingStageRefRepository trainingStageRefRepository;
    private final SportKindRefRepository sportKindRefRepository;
    private final TourismKindRefRepository tourismKindRefRepository;
    private final CreationKindRefRepository creationKindRefRepository;
    private final SportAgeRefRepository sportAgeRefRepository;


    private final ExcelAsyncProcesses excelAsync;

    @Value("${roles.global.employeeId}")
    private Long globalRoleEmployee;

    @Value("${excel.fileMaxRowCount}")
    private Integer fileMaxRowCount;

    @Value("${excel.partitionSize}")
    private Integer importPartitionSize;

    @Value("${roles.operator.adminOO}")
    private Long adminOO;

    @Value("${roles.operator.employeeOO}")
    private Long employeeOO;

    @Value("${roles.operator.teacherOO}")
    private Long teacherOO;


    private static List<String> PERS_INFO_HEADERS = Arrays.asList("Фамилия", "Имя", "Отчество", "Дата рождения", "СНИЛС",
            "Телефон", "e-mail", "ОО", "Параллель", "Класс");

    private static Map<String, Integer> VALID_HEADERS_MAP = new HashMap<String, Integer>() {
        {
            put("1,7,8", -360146389);
            put("2,11", -537981481);
            put("2,12,13", -1099389131);
            put("2,14,15", 1437328198);
            put("2,14,16", -1689615472);
            put("3,20,21", 1331947078);
            put("3,20,22", 2109569972);
            put("3,20,75", -1608478137);
            put("3,23,24", 989890523);
            put("3,23,25", 989890523);
            put("3,27,28", -178793539);
            put("3,27,29", 184726455);
            put("3,27,30", 1785555899);
            put("3,31,32", -1643541800);
            put("3,31,33", 721863363);
            put("3,31,34", -189965232);
            put("3,31,35", 1969775948);
            put("4,37,38", 556909760);
            put("4,40,41", -81982728);
            put("4,40,42", -81982728);
            put("4,43,44", 1886145825);
            put("5,47,48", -446346728);
            put("5,47,63", -1115379482);
            put("6,49,50", 370281327);
            put("6,49,51", 370281327);
            put("6,52,53", -127714202);
            put("6,54,55", -432822367);
            put("6,54,56", -1348955199);
            put("6,54,57", -432822367);
            put("6,54,58", -432822367);
            put("6,54,59", 1449120117);
            put("6,60,61", -2070688632);
            put("68,70,72", -310862557);
            put("68,71,73", -561339794);
        }
    };

    private static ExcelSearchParamsDTO parseToSearchParams(List<String> searchFields, List<String> headers,
                                                            ExcelDTO excelDTO, Integer rowNum) {
        List<AttachmentsDTO.Comment> errorComments = new ArrayList<>();
        ExcelSearchParamsDTO paramsDTO = new ExcelSearchParamsDTO();
        paramsDTO.setLastname(Strings.emptyToNull(searchFields.get(0)));
        paramsDTO.setFirstname(Strings.emptyToNull(searchFields.get(1)));
        paramsDTO.setPatronymic(Strings.emptyToNull(searchFields.get(2)));
        Optional.ofNullable(Strings.emptyToNull(searchFields.get(3))).ifPresent(x -> {
            try {
                paramsDTO.setBirthdate(ExcelUtils.tryToParseLocalDate(x, headers.get(3), errorComments));
            } catch (Exception ex) {
                AttachmentsDTO.Comment comment = new AttachmentsDTO.Comment();
                comment.setDescription("Неверный формат даты рождения");
                comment.setParam(headers.get(3));
                comment.setValue(searchFields.get(3));
                errorComments.add(comment);
            }
        });
        paramsDTO.setSnils(Strings.emptyToNull(searchFields.get(4)));
        paramsDTO.setPhone(Strings.emptyToNull(searchFields.get(5)));
        paramsDTO.setMail(Strings.emptyToNull(searchFields.get(6)));
        paramsDTO.setSchoolId(Strings.emptyToNull(searchFields.get(7)));
        paramsDTO.setParallel(Strings.emptyToNull(searchFields.get(8)));
        paramsDTO.setGrade(Strings.emptyToNull(searchFields.get(9)));
        if (!errorComments.isEmpty()) {
            AttachmentsDTO.Record record = new AttachmentsDTO.Record();
            record.setFirstname(paramsDTO.getFirstname());
            record.setLastname(paramsDTO.getLastname());
            record.setPatronymic(paramsDTO.getPatronymic());
            record.setErrorType("Неверный формат данных");
            record.setComment(errorComments);
            record.setRowNum(rowNum);
            excelDTO.getIncorrectRows().add(record);
        }
        return paramsDTO;
    }

    private <T extends PersonallyEntity> void parseForResponse(List<String> fieldList, List<String> headers,
                                                               String templateConcatenation, ExcelDTO excelDTO, Integer rowNum,
                                                               AttachmentDTO attachment, boolean isEmployee) {
        T first = null;
        T second = null;
        List<String> stopWords = stopWordRepository.findAll().stream().map(StopWordsRef::getWord).collect(Collectors.toList());
        switch (templateConcatenation) {
            case ("1,7,8"): {
                first = Utils.cast(eventMapper.uploadStudyEventRewardOlympiad(fieldList, headers, excelDTO, stopWords, isEmployee));
                second = Utils.cast(rewardMapper.uploadStudyEventRewardOlympiad(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("2,11"): {
                first = Utils.cast(projectMapper.uploadScienceProject(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("2,12,13"): {
                first = Utils.cast(employmentMapper.uploadScienceEmploymentUnit(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("2,14,15"):
            case ("2,14,16"): {
                first = Utils.cast(eventMapper.uploadScienceEventReward(fieldList, headers, excelDTO, stopWords, isEmployee));
                second = Utils.cast(rewardMapper.uploadScienceEventReward(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("3,20,21"): {
                first = Utils.cast(affilationMapper.uploadSportAffilationClub(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("3,20,22"): {
                first = Utils.cast(affilationMapper.uploadSportAffilationTeam(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("3,20,75"): {
                first = Utils.cast(affilationMapper.uploadSportAffilation(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("3,23,24"):
            case ("3,23,25"): {
                first = Utils.cast(employmentMapper.uploadSportEmployment(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("3,27,28"): {
                first = Utils.cast(eventMapper.uploadSportEventRewardCompetition(fieldList, headers, excelDTO, stopWords, isEmployee));
                second = Utils.cast(sportRewardMapper.uploadSportEventRewardCompetition(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("3,27,29"):
            case ("3,27,30"): {
                first = Utils.cast(eventMapper.uploadSportEventReward(fieldList, headers, excelDTO, stopWords, isEmployee));
                if (StringUtils.isNotBlank(fieldList.get(29))) {
                    second = Utils.cast(sportRewardMapper.uploadSportEventReward(fieldList, headers, excelDTO, stopWords, isEmployee));
                }
                break;
            }
            case ("3,31,32"): {
                first = Utils.cast(sportRewardMapper.uploadSportRewardGTO(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("3,31,33"): {
                first = Utils.cast(sportRewardMapper.uploadSportRewardSportRank(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("3,31,34"): {
                first = Utils.cast(sportRewardMapper.uploadSportRewardSportGrade(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("3,31,35"): {
                first = Utils.cast(sportRewardMapper.uploadSportRewardTourism(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("4,37,38"): {
                first = Utils.cast(affilationMapper.uploadCreationAffilationCollective(
                        fieldList, headers, excelDTO, stopWords, isEmployee, 38L));
                break;
            }
            case ("4,40,41"):
            case ("4,40,42"): {
                first = Utils.cast(employmentMapper.uploadCreationEmployment(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("4,43,44"): {
                first = Utils.cast(eventMapper.uploadCreationEventRewardContest(fieldList, headers, excelDTO, stopWords, isEmployee));
                second = Utils.cast(rewardMapper.uploadCreationEventRewardContest(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("5,47,48"): {
                first = Utils.cast(eventMapper.uploadCultureEventRewardVisit(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("5,47,63"): {
                first = Utils.cast(eventMapper.uploadCultureEventRewardOnlineVisit(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("6,49,50"): {
                first = Utils.cast(affilationMapper.uploadCivilAffilation(
                        fieldList, headers, excelDTO, stopWords, isEmployee, 50L));
                break;
            }
            case ("6,49,51"): {
                first = Utils.cast(affilationMapper.uploadCivilAffilation(
                        fieldList, headers, excelDTO, stopWords, isEmployee, 51L));
                break;
            }
            case ("6,52,53"): {
                first = Utils.cast(employmentMapper.uploadCivilEmploymentUnit(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("6,54,55"):
            case ("6,54,56"):
            case ("6,54,57"):
            case ("6,54,58"): {
                first = Utils.cast(eventMapper.uploadCivilEventReward(fieldList, headers, excelDTO, attachment, stopWords, isEmployee));
                second = Utils.cast(rewardMapper.uploadCivilEventReward(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("6,54,59"): {
                first = Utils.cast(eventMapper.uploadCivilEventRewardQuiz(fieldList, headers, excelDTO, stopWords, isEmployee));
                second = Utils.cast(rewardMapper.uploadCivilEventRewardQuiz(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("6,60,61"): {
                first = Utils.cast(rewardMapper.uploadCivilRewardGradeAndRank(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("68,70,72"): {
                first = Utils.cast(eventMapper.uploadMyProfessionEventReward(fieldList, headers, excelDTO, stopWords, isEmployee));
                second = Utils.cast(rewardMapper.uploadMyProfessionEventReward(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            case ("68,71,73"): {
                first = Utils.cast(worldSkillsMapper.map(fieldList, headers, excelDTO, stopWords, isEmployee));
                break;
            }
            default: {
                throw PortfolioException.get467();
            }
        }
        List<AttachmentsDTO.Record> records = excelDTO.getIncorrectRows().stream()
                .filter(x -> isNull(x.getRowNum()))
                .collect(Collectors.toList());
        if (!records.isEmpty()) {
            excelDTO.getIncorrectRows().stream().filter(x -> isNull(x.getRowNum())).forEach(x -> x.setRowNum(rowNum));
        } else {
            excelDTO.addPersonalEntities(first, second, rowNum);
        }
    }

    @SneakyThrows
    public void parseExcel(byte[] file, Integer sheetNumber, String templateConcatenation,
                           AttachmentDTO attachment, boolean isEmployee, boolean isTeacher,
                           ExcelCache excelCache, List<Integer> employeeOrgIds, List<Long> categoryCodesToOperator,
                           AccessTokenPayloadDto tokenPayload) {
        XSSFWorkbook workbook = new XSSFWorkbook(new ByteArrayInputStream(file));
        XSSFSheet sheet = workbook.getSheetAt(sheetNumber);
        Row headersRow = Lists.newArrayList(sheet.rowIterator()).stream().findFirst().orElse(null);

        PortfolioException.check(nonNull(headersRow), PortfolioException.get466());
        List<String> headers = new ArrayList<>();
        headersRow.cellIterator()
                .forEachRemaining(x -> headers.add(new DataFormatter().formatCellValue(x)));
        PortfolioException.check(Objects.nonNull(VALID_HEADERS_MAP.get(templateConcatenation)),
                PortfolioException.get466());
        PortfolioException.check(
                VALID_HEADERS_MAP.get(templateConcatenation).equals(String.join(",", headers).hashCode()),
                PortfolioException.get466());

        sheet.removeRow(headersRow);

        PortfolioException.check(sheet.getPhysicalNumberOfRows() <= fileMaxRowCount,
                PortfolioException.getValidError(
                        PortfolioException.get486(fileMaxRowCount.toString(), String.valueOf(sheet.getPhysicalNumberOfRows()))));

        int notNullCount = 0;
        int blankRowsCount = 0;
        boolean isNullRow = false;
        for (Row row : sheet) {
            int nullCells = 0;
            for (Cell cell : row) {

                if (cell.getCellType() != CellType.BLANK) {
                    if (cell.getCellType() != CellType.STRING ||
                            cell.getStringCellValue().length() > 0) {
                        blankRowsCount = 0;
                        notNullCount++;
                        break;
                    }
                } else {
                    nullCells++;
                    if (row.getLastCellNum() == nullCells) {
                        blankRowsCount++;
                        notNullCount++;
                        if (blankRowsCount == 10) {
                            isNullRow = true;
                        }
                    }
                }
            }
            if (isNullRow) {
                notNullCount -= 10;
                break;
            }
        }

        excelCache.setPartitionsNumber(notNullCount % importPartitionSize == 0
                ? notNullCount / importPartitionSize
                : (notNullCount / importPartitionSize) + 1);
        excelAsync.updateExcelCache(excelCache);
        Integer part = 1;
        List<ExcelDTO> partition = new ArrayList<>();
        boolean nextRowIsEmpty = false;
        for (int num = 1; num <= notNullCount; num++) {
            List<String> fieldsList = new ArrayList<>();
            List<String> searchList = new ArrayList<>();
            Row row = sheet.getRow(num);
            for (int i = 0; i < row.getLastCellNum(); i++) {
                if (i < PERS_INFO_HEADERS.size()) {
                    searchList.add(getCellValue(row.getCell(i)));
                    fieldsList.add(getCellValue(row.getCell(i)));
                } else {
                    fieldsList.add(getCellValue(row.getCell(i)));
                }
            }
            if (fieldsList.size() < headers.size()) {
                IntStream.range(fieldsList.size(), headers.size()).forEach(x -> fieldsList.add(StringUtils.EMPTY));
            }
            try {
                int rowNum = row.getRowNum() + 1;
                if (fieldsList.stream().filter(Objects::nonNull).anyMatch(x -> !x.isEmpty())) {
                    ExcelDTO excelDTO = new ExcelDTO();
                    ExcelSearchParamsDTO searchParams = parseToSearchParams(searchList, headers, excelDTO, rowNum);
                    excelDTO = partition.stream()
                            .filter(x -> x.getPersonsSearchParams().equals(searchParams))
                            .findFirst()
                            .orElse(excelDTO);
                    if (isNull(excelDTO.getPersonsSearchParams())) {
                        excelDTO.setPersonsSearchParams(searchParams);
                        excelDTO.setPersonalEntities(new ArrayList<>());
                        parseForResponse(fieldsList, headers, templateConcatenation, excelDTO, rowNum, attachment, isEmployee);
                        partition.add(excelDTO);
                    } else {
                        parseForResponse(fieldsList, headers, templateConcatenation, excelDTO, rowNum, attachment, isEmployee);
                    }
                }
            } catch (PortfolioException pex) {
                throw pex;
            } catch (Exception ex) {
                throw PortfolioException.get999();
            }

            if (row.getRowNum() % importPartitionSize == 0) {
                if (partition.size() != 0) {
                    processPartitionData(part, partition, isEmployee, isTeacher, employeeOrgIds,
                            categoryCodesToOperator, tokenPayload, excelCache, attachment);
                    partition = new ArrayList<>();
                }

                if (part.equals(excelCache.getPartitionsNumber())) {
                    excelCache.setStatus(ImportStatus.PREVIEW.name());
                }
                excelAsync.updateExcelCache(excelCache);
                part++;
            }
        }
        if (notNullCount < importPartitionSize || !partition.isEmpty()) {
            processPartitionData(part, partition, isEmployee, isTeacher, employeeOrgIds,
                    categoryCodesToOperator, tokenPayload, excelCache, attachment);
            partition.clear();
            if (part.equals(excelCache.getPartitionsNumber())) {
                excelCache.setStatus(ImportStatus.PREVIEW.name());
            }
            excelAsync.updateExcelCache(excelCache);
        }
    }

    private static String getCellValue(Cell cell) {
        if (isNull(cell)) {
            return StringUtils.EMPTY;
        } else if (cell.getCellType().equals(CellType.NUMERIC) && DateUtil.isCellDateFormatted(cell)) {
            return cell.getDateCellValue().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().toString().trim();
        } else {
            return new DataFormatter().formatCellValue(cell).trim();
        }
    }

    @SneakyThrows
    private void setIndependentValues(ru.portfolio.ax.rest.dto.AttachmentDTO attachment, List<PersonalEntities> personalEntities) {
        personalEntities.forEach(x -> {
            if (Objects.nonNull(x.getFirst())) {
                Utils.fillDeclaredFieldWithValue(x.getFirst(), "category", crudService.find(attachment.getCategoryCode(), SectionRef.class));
                Utils.fillDeclaredFieldWithValue(x.getFirst(), "categoryCode", attachment.getCategoryCode());

                Utils.fillDeclaredFieldWithValue(x.getFirst(), "dataKind", attachment.getDataKind());
                if (Objects.nonNull(attachment.getMainTypeCode())) {
                    Utils.fillDeclaredFieldWithValue(x.getFirst(), "type", crudService.find(attachment.getMainTypeCode(), SectionRef.class));
                    Utils.fillDeclaredFieldWithValue(x.getFirst(), "typeCode", Long.valueOf(attachment.getMainTypeCode()));
                }
                x.getFirst().reachTransient(crudService);
            }

            if (Objects.nonNull(x.getSecond())) {
                Utils.fillDeclaredFieldWithValue(x.getSecond(), "category", crudService.find(attachment.getCategoryCode(), SectionRef.class));
                Utils.fillDeclaredFieldWithValue(x.getSecond(), "categoryCode", attachment.getCategoryCode());


                if (Objects.nonNull(attachment.getSecondaryTypeCode())) {
                    SectionRef sectionRef = crudService.find(attachment.getSecondaryTypeCode(), SectionRef.class);
                    Utils.fillDeclaredFieldWithValue(x.getSecond(), "dataKind", sectionRef.getParentId());

                    Utils.fillDeclaredFieldWithValue(x.getSecond(), "type",
                            crudService.find(attachment.getSecondaryTypeCode(), SectionRef.class));
                    Utils.fillDeclaredFieldWithValue(x.getSecond(), "typeCode",
                            Long.valueOf(attachment.getSecondaryTypeCode()));
                }
                x.getSecond().reachTransient(crudService);
            }
        });
    }

    @Transactional
    public void tryParseFile(byte[] file, ru.portfolio.ax.rest.dto.AttachmentDTO attachment,
                             String bearer, ExcelCache cache) {
        try {
            parseFile(file, attachment, bearer, cache);
        } catch (PortfolioException pex) {
            cache.setErrorMessage(pex.getCodifiedMessage().getDescription());
            cache.setStatus(ImportStatus.FAIL.name());
            excelAsync.updateExcelCache(cache);
        } catch (Exception e) {
            cache.setErrorMessage(e.getMessage());
            cache.setStatus(ImportStatus.FAIL.name());
            excelAsync.updateExcelCache(cache);
        }
    }

    @SneakyThrows
    @Transactional
    public <T extends PersonallyEntity> void parseFile(byte[] file, ru.portfolio.ax.rest.dto.AttachmentDTO attachment,
                                                       String bearer, ExcelCache cache) {
        if (nonNull(attachment.getCacheUUID())) {
            cache.setStatus(ImportStatus.SAVING.name());
            excelAsync.updateExcelCache(cache);
            AttachmentsDTO cacheResponse = getAttachmentsFromCache(attachment, bearer);
            if (cacheResponse != null) return;
        }

        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        AggregatedGlobalRole aggregatedGlobalRole = authService.parseRLS(tokenPayload.getRls())
                .stream().filter(x -> globalRoleEmployee.equals(x.getId())).findFirst().orElse(null);
        boolean isEmployee = false;
        boolean isTeacher = false;
        List<Integer> employeeOrgIds = new ArrayList<>();
        List<Long> categoryCodesToOperator = new ArrayList<>();
        if (Objects.nonNull(aggregatedGlobalRole)) {
//            isEmployee = aggregatedGlobalRole.getLocalRoles().stream().noneMatch(x -> LongStream.range(32, 40)
//                    .boxed().collect(Collectors.toList()).contains(x.getId()));

            if (aggregatedGlobalRole.getLocalRoles().stream().noneMatch(x -> LongStream.range(32, 40)
                    .boxed().collect(Collectors.toList()).contains(x.getId()))) {
                if (aggregatedGlobalRole.getLocalRoles().stream().anyMatch(x -> adminOO.equals(x.getId()))) {
                    employeeOrgIds = aggregatedGlobalRole.getLocalRoles().stream().filter(x -> adminOO.equals(x.getId()))
                            .map(AggregatedGlobalRole.AggregatedLocalRole::getOrgIds).flatMap(Collection::stream)
                            .collect(Collectors.toList());
                    isEmployee = true;
                } else if (aggregatedGlobalRole.getLocalRoles().stream().anyMatch(x -> employeeOO.equals(x.getId()))) {
                    employeeOrgIds = aggregatedGlobalRole.getLocalRoles().stream().filter(x -> employeeOO.equals(x.getId()))
                            .map(AggregatedGlobalRole.AggregatedLocalRole::getOrgIds).flatMap(Collection::stream)
                            .collect(Collectors.toList());
                    isEmployee = true;
                } else if (aggregatedGlobalRole.getLocalRoles().stream().anyMatch(x -> teacherOO.equals(x.getId()))) {
                    employeeOrgIds = aggregatedGlobalRole.getLocalRoles().stream().filter(x -> teacherOO.equals(x.getId()))
                            .map(AggregatedGlobalRole.AggregatedLocalRole::getOrgIds).flatMap(Collection::stream)
                            .collect(Collectors.toList());
                    isTeacher = true;
                } else if (authComponent.containsOperatorLocalRole(aggregatedGlobalRole.getLocalRoles())) {
                    categoryCodesToOperator = authComponent.getAvailableCategoryCodesToOperator(aggregatedGlobalRole.getLocalRoles());
                }
            }
        }


        String templateConcatenation = Utils.join(Arrays.asList(attachment.getCategoryCode(), attachment.getDataKind(), attachment.getMainTypeCode()));
        PortfolioException.check(Objects.nonNull(templateConcatenation), PortfolioException.get466());


        cache.setIncorrectRecords(0);
        cache.setCorrectRecords(0);
        excelAsync.updateExcelCache(cache);

        parseExcel(file, 0, templateConcatenation, attachment, isEmployee, isTeacher,
                cache, employeeOrgIds, categoryCodesToOperator, tokenPayload);
    }

    private <T extends PersonallyEntity> void processPartitionData(Integer partitionNumber, List<ExcelDTO> partitionData,
                                                                   boolean isEmployee, boolean isTeacher, List<Integer> employeeOrgIds,
                                                                   List<Long> categoryCodesToOperator, AccessTokenPayloadDto tokenPayload,
                                                                   ExcelCache excelCache, ru.portfolio.ax.rest.dto.AttachmentDTO attachment) {
        Integer correctRow = 0;
        setIndependentValues(attachment,
                partitionData.stream().map(ExcelDTO::getPersonalEntities)
                        .flatMap(Collection::stream).collect(Collectors.toList()));
        List<AttachmentsDTO.Record> incorrectRows = new ArrayList<>();
        List<T> firstsToDelete = new ArrayList<>();
        List<String> correctSeconds = new ArrayList<>();
        for (ExcelDTO excelDTO : partitionData) {
            List<PersonDTO> personDTOS = contingentService.getPersonByParams(excelDTO.getPersonsSearchParams().getLastname(),
                    excelDTO.getPersonsSearchParams().getFirstname(), excelDTO.getPersonsSearchParams().getPatronymic(),
                    excelDTO.getPersonsSearchParams().getBirthdate(), excelDTO.getPersonsSearchParams().getSnils());
            if (personDTOS.size() > 1) {
                personDTOS = findPersonBySnils(nsiService, excelDTO, personDTOS);
            } else if (personDTOS.size() < 1) {
                excelDTO.getPersonalEntities().forEach(x -> {
                    excelDTO.getIncorrectRows()
                            .add(fillRecord(excelDTO.getPersonsSearchParams(), "Учащийся не найден", x.getRowNum()));
                });
            }
            if (personDTOS.size() > 1) {
                excelDTO.getPersonalEntities().forEach(x -> {
                    excelDTO.getIncorrectRows()
                            .add(fillRecord(excelDTO.getPersonsSearchParams(), "Найдено больше одного учащегося", x.getRowNum()));
                });
            } else if (personDTOS.size() == 1) {
                if (isEmployee) {
                    if (!checkForOrganization(employeeOrgIds, personDTOS.stream().findFirst().get())) {
                        excelDTO.getPersonalEntities().forEach(x -> {
                            AttachmentsDTO.Record record = fillRecord(excelDTO.getPersonsSearchParams(),
                                    "Идентификаторы организаций загружающего сотрудника и ученика не совпадают", x.getRowNum());
                            excelDTO.getIncorrectRows()
                                    .add(record);
                            incorrectRows.add(record);
                        });
                        continue;
                    }
                } else if (isTeacher) {
                    if (!checkForClass(tokenPayload.getStf(), personDTOS.stream().findFirst().get())) {
                        excelDTO.getPersonalEntities().forEach(x -> {
                            AttachmentsDTO.Record record = fillRecord(excelDTO.getPersonsSearchParams(),
                                    "Ученик не относится к загружающему учителю", x.getRowNum());
                            excelDTO.getIncorrectRows()
                                    .add(record);
                            incorrectRows.add(record);
                        });
                        continue;
                    }
                }
                String personId = personDTOS.stream().findFirst().get().getPersonId().toString();
                excelDTO.getPersonsSearchParams().depersonalization(personId);
                List<PersonalEntities> pairs = excelDTO.getPersonalEntities();
                for (PersonalEntities pair : pairs) {
                    boolean validRow = true;
                    T first = CastUtils.cast(pair.getFirst());
                    first.setPersonId(personId);
                    first.setCreatorId(tokenPayload.getStf());
                    first.setIsImport(true);
                    first.setIsDelete(false);
                    first.reach(crudService);
                    first.setHashCode(Utils.createHash(first));
                    if (crudService.existPersByHash(first.getHashCode(), first.getClass())) { //todo add and check hash list
                        excelDTO.getIncorrectRows()
                                .add(fillRecord(excelDTO.getPersonsSearchParams(), "Данная запись уже существует", pair.getRowNum()));
                        validRow = false;
                    } else {
                        crudService.excelCreate(first);
                        if (Objects.nonNull(pair.getSecond())) {
                            if (Objects.nonNull(pair.getSecond())) {
                                T second = CastUtils.cast(pair.getSecond());
                                second.setPersonId(personId);
                                second.setCreatorId(tokenPayload.getStf());
                                second.setIsImport(true);
                                second.setIsDelete(false);
                                linkReward(first, second);
                                second.reach(crudService);
                                second.setHashCode(Utils.createHash(second));
                                if (crudService.existPersByHash(second.getHashCode(), second.getClass()) ||
                                        correctSeconds.stream().anyMatch(x -> x.equals(second.getHashCode()))) { //todo add and check hash list
                                    excelDTO.getIncorrectRows()
                                            .add(fillRecord(excelDTO.getPersonsSearchParams(), "Данная запись уже существует", pair.getRowNum()));
                                    validRow = false;
                                    firstsToDelete.add(first);
                                } else {
                                    correctSeconds.add(second.getHashCode());
                                    firstsToDelete.add(first);
                                }
                            }
                        } else {
                            firstsToDelete.add(first);
                        }
                    }
                    if (validRow) {
                        correctRow++;
                    }
                }
            }
            if (!excelDTO.getIncorrectRows().isEmpty()) {
                incorrectRows.addAll(excelDTO.getIncorrectRows());
            }
        }
        if (!firstsToDelete.isEmpty()) {
            crudService.deleteAll(firstsToDelete);
        }
        ExcelCachePartition partition = new ExcelCachePartition();
        partition.setExcelCache(excelCache);
        partition.setPartitionNumber(partitionNumber);
        partition.setCorrectRecords(correctRow);
        partition.setIncorrectRecords(incorrectRows.stream()
                .map(AttachmentsDTO.Record::getRowNum)
                .collect(Collectors.toSet())
                .size());

        partition.setJsonData(objectMapper.valueToTree(partitionData));
        excelAsync.saveExcelCachePartition(partition);

        excelCache.setIncorrectRecords(excelCache.getIncorrectRecords() + partition.getIncorrectRecords());
        excelCache.setCorrectRecords(excelCache.getCorrectRecords() + partition.getCorrectRecords());
    }

    private AttachmentsDTO getAttachmentsFromCache(ru.portfolio.ax.rest.dto.AttachmentDTO attachment, String bearer) {
        ExcelCache cache = excelCacheRepository.findByCacheUUID(attachment.getCacheUUID());
        AttachmentsDTO attachmentsDTO = new AttachmentsDTO();
        attachmentsDTO.setIncorrectRecords(new AttachmentsDTO.IncorrectRecords());

        if (Objects.nonNull(cache)) {

            AtomicReference<Integer> correctCount = new AtomicReference<>(0);
            attachmentsDTO.setCacheUUID(attachment.getCacheUUID());

            for (int i = 0; i < cache.getPartitionsNumber(); i++) {
                excelAsync.processPartition(cache, attachment, bearer, attachmentsDTO, correctCount);
            }
//            if (cache.getSaveCheck()) {
//                excelCacheRepository.delete(cache);
//            }
            attachmentsDTO.setCorrectRecordsCount(correctCount.get());

            attachmentsDTO.getIncorrectRecords().getRecords().sort(Comparator.comparing(AttachmentsDTO.Record::getRowNum));
            long incorrectRecordsCount = attachmentsDTO.getIncorrectRecords().getRecords().stream()
                    .map(AttachmentsDTO.Record::getRowNum).count();
            attachmentsDTO.setIncorrectRecordsCount((int) incorrectRecordsCount);
            attachmentsDTO.setPartitionsNumber(cache.getPartitionsNumber());

            ImportHistory importHistory = new ImportHistory();
            importHistory.setCreatorId(cache.getUserId());
            importHistory.setCreationDate(LocalDateTime.now());
            importHistory.setCategoryCode(cache.getCategoryCode());
            importHistory.setDataKind(cache.getDataKind());
            importHistory.setTypeCode(cache.getTypeCode());
            importHistory.setFileName(cache.getFilename());
            importHistory.setCorrectCount(attachmentsDTO.getCorrectRecordsCount());
            importHistory.setIncorrectCount(attachmentsDTO.getIncorrectRecordsCount());
            importHistory.setReport(buildReport(cache.getCategoryCode(), cache.getTypeCode(), attachmentsDTO, Long.valueOf(cache.getUserId())));
            importHistoryRepository.save(importHistory);

            cache.setStatus(ImportStatus.SUCCESS.name());
            excelCacheRepository.save(cache);
            return attachmentsDTO;
        } else {
            return null;
        }
    }

    @SneakyThrows
    private String buildReport(Integer categoryCode, Integer typeCode, AttachmentsDTO attachmentsDTO, Long userId) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("FirstSheet");

        CellStyle styleRowHead = workbook.createCellStyle();
        XSSFFont fontRowHead = workbook.createFont();
        fontRowHead.setFontName("Arial");
        fontRowHead.setFontHeightInPoints((short) 16);
        fontRowHead.setBold(true);
        styleRowHead.setFont(fontRowHead);

        XSSFRow rowHead = sheet.createRow(0);
        rowHead.createCell(0).setCellValue("ФИО Оператора");
        rowHead.getCell(0).setCellStyle(styleRowHead); //todo simplify
        rowHead.createCell(1).setCellValue("Категория");
        rowHead.getCell(1).setCellStyle(styleRowHead);
        rowHead.createCell(2).setCellValue("Вид");
        rowHead.getCell(2).setCellStyle(styleRowHead);
        rowHead.createCell(3).setCellValue("Число успешно загруженных строк");
        rowHead.getCell(3).setCellStyle(styleRowHead);
        rowHead.createCell(4).setCellValue("Число не загруженных строк");
        rowHead.getCell(4).setCellStyle(styleRowHead);


        XSSFRow rowValue = sheet.createRow(1);
        rowValue.createCell(0).setCellValue(userId.toString());
        rowValue.createCell(1).setCellValue(crudService.findRef(categoryCode, SectionRef.class).getValue());
        String resultTypeCode = nonNull(typeCode) ? crudService.findRef(typeCode, SectionRef.class).getValue() : "";
        rowValue.createCell(2).setCellValue(resultTypeCode);
        rowValue.createCell(3).setCellValue(attachmentsDTO.getCorrectRecordsCount());
        rowValue.createCell(4).setCellValue(attachmentsDTO.getIncorrectRecordsCount());

        XSSFRow rowErrorHead = sheet.createRow(2);
        rowErrorHead.createCell(0).setCellValue("Номер строки");
        rowErrorHead.getCell(0).setCellStyle(styleRowHead);
        rowErrorHead.createCell(1).setCellValue("ФИО");
        rowErrorHead.getCell(1).setCellStyle(styleRowHead);
        rowErrorHead.createCell(2).setCellValue("Ошибки");
        rowErrorHead.getCell(2).setCellStyle(styleRowHead);

        Integer rowNum = 2;
        for (AttachmentsDTO.Record record : attachmentsDTO.getIncorrectRecords().getRecords()) {
            rowNum += 1;
            XSSFRow fileRow = sheet.createRow(rowNum);
            fileRow.createCell(0).setCellValue(record.getRowNum());
            fileRow.createCell(1).setCellValue(record.getUuid());
            fileRow.createCell(2).setCellValue(record.getErrorType());
        }

        ByteArrayOutputStream fileOut = new ByteArrayOutputStream();
        workbook.write(fileOut);
        workbook.close();
        fileOut.close();
        return Base64.getEncoder().encodeToString(fileOut.toByteArray());
    }

    @SneakyThrows
    public byte[] fillPersonalData(String file) {
        InputStream is = new ByteArrayInputStream(Base64.getDecoder().decode(file.getBytes()));
        XSSFWorkbook workbook = new XSSFWorkbook(is);
        is.close();
        Cell employeeCell = workbook.getSheetAt(0).getRow(1).getCell(0);
        if (employeeCell.getCellType().equals(CellType.STRING)) {
            String stringValue = employeeCell.getStringCellValue();
            UserContextDTO.Info.UserFio fio = safetyGet(() -> nsiService
                    .getEmployee(stringValue)
                    .map(UserContextDTO.Info.UserFio::build)
                    .orElse(null));
            if (nonNull(fio)) {
                employeeCell.setCellValue(fio.getLastName() + " " + fio.getFirstName() + " " + fio.getPatronymic());
            }
        }

        Map<String, Integer> personIds = new HashMap<>();
        for (int i = 3; i <= workbook.getSheetAt(0).getLastRowNum(); i++) {
            personIds.put(new DataFormatter().formatCellValue(workbook.getSheetAt(0).getRow(i).getCell(1)).trim(), i);
        }

        List<List<String>> listPersonIds = Lists.partition(
                personIds.keySet().stream().filter(Objects::nonNull).filter(x -> !x.isEmpty()).collect(Collectors.toList()), 90);
        List<PersonDTO> persons = listPersonIds.stream()
                .map(contingentService::getPersonsFIO)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        persons.forEach(person -> {
            workbook.getSheetAt(0)
                    .getRow(personIds.get(person.getPersonId().toString()))
                    .getCell(1)
                    .setCellValue(String.join(" ", person.getLastname(), person.getFirstname(), person.getPatronymic()));
        });

        ByteArrayOutputStream fileOut = new ByteArrayOutputStream();
        workbook.write(fileOut);
        workbook.close();
        fileOut.close();
        return fileOut.toByteArray();
    }

    private <T extends PersonallyEntity> void linkReward(T entity, T target) { //mb add generic class for reward and sportReward
        if (target.getClass().equals(Reward.class)) {
            Reward reward = (Reward) target;
            reward.setEntityId(entity.getId().toString());
        }
        if (target.getClass().equals(SportReward.class)) {
            SportReward reward = (SportReward) target;
            reward.setEntityId(entity.getId().toString());
        }
    }

    private <T extends PersonallyEntity> void unlinkReward(T entity) {  //mb add generic class for reward and sportReward
        if (Objects.isNull(entity)) {
            return;
        }
        entity.setId(null);
        if (entity.getClass().equals(Reward.class)) {
            Reward reward = (Reward) entity;
            reward.setEntityId(null);
        }
        if (entity.getClass().equals(SportReward.class)) {
            SportReward reward = (SportReward) entity;
            reward.setEntityId(null);
        }
    }

    @SneakyThrows
    public byte[] buildReport(List<ActionHistory> list) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("FirstSheet");

        CellStyle styleRowHead = workbook.createCellStyle();
        XSSFFont fontRowHead = workbook.createFont();
        fontRowHead.setFontName("Arial");
        fontRowHead.setFontHeightInPoints((short) 16);
        fontRowHead.setBold(true);
        styleRowHead.setFont(fontRowHead);

        XSSFRow rowHead = sheet.createRow(0);
        rowHead.createCell(0).setCellValue("Дата и время события");
        rowHead.getCell(0).setCellStyle(styleRowHead);

        rowHead.createCell(1).setCellValue("Вид события");
        rowHead.getCell(1).setCellStyle(styleRowHead);
        rowHead.createCell(2).setCellValue("Описание события");
        rowHead.getCell(2).setCellStyle(styleRowHead);
        rowHead.createCell(3).setCellValue("Результат события (наличие ошибок)");
        rowHead.getCell(3).setCellStyle(styleRowHead);
        rowHead.createCell(4).setCellValue("Id пользователя");
        rowHead.getCell(4).setCellStyle(styleRowHead);
        rowHead.createCell(5).setCellValue("ФИО пользователя");
        rowHead.getCell(5).setCellStyle(styleRowHead);
        rowHead.createCell(6).setCellValue("Роль пользователя");
        rowHead.getCell(6).setCellStyle(styleRowHead);
        rowHead.createCell(7).setCellValue("Id учащегося");
        rowHead.getCell(7).setCellStyle(styleRowHead);
        rowHead.createCell(8).setCellValue("ФИО учащегося");
        rowHead.getCell(8).setCellStyle(styleRowHead);

        List<String> meshIds = list.stream().map(ActionHistory::getUserId).filter(Utils::isValidUUID).distinct().collect(Collectors.toList());
        meshIds.addAll(list.stream().map(ActionHistory::getPersonId).distinct().filter(Objects::nonNull).collect(Collectors.toList()));
        List<String> stfIds = list.stream().map(ActionHistory::getUserId).filter(x -> !Utils.isValidUUID(x)).distinct().collect(Collectors.toList());

        List<List<String>> listPersonIds = Lists.partition(meshIds, 50);
        List<PersonDTO> personDTOS = listPersonIds.stream()
                .map(contingentService::getPersons)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        List<NsiDTO.Response> employers = new ArrayList<>();
        if (!stfIds.isEmpty()) {
            employers.addAll(nsiService.getEmployee(stfIds));
        }

        Integer rowNum = 0;
        for (ActionHistory record : list) {
            rowNum += 1;
            XSSFRow fileRow = sheet.createRow(rowNum);
            fileRow.createCell(0).setCellValue(record.getActionDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            String actionType = record.getActionTypeCode().equals(1) ? "Пользовательское" : "Системное";
            ActionKindRef actionKind = actionKindRefRepository.findById(record.getActionKindCode()).orElse(null);
            //fileRow.createCell(1).setCellValue(actionType);
            fileRow.createCell(1).setCellValue(nonNull(actionKind) ? actionKind.getValue() : "");
            fileRow.createCell(4).setCellValue(record.getUserId());
            fileRow.createCell(7).setCellValue(record.getPersonId());
            fileRow.createCell(2).setCellValue(record.getActionDescription());
            fileRow.createCell(3).setCellValue(record.getActionContent());
            if (Utils.isValidUUID(record.getUserId())) {
                PersonDTO person = personDTOS.stream()
                        .filter(x -> x.getPersonId().toString().equals(record.getUserId()))
                        .findFirst().orElse(null);
                if (nonNull(person)) {
                    String fio = person.getLastname() + " " + person.getFirstname();
                    if (nonNull(person.getPatronymic())) fio += " " + person.getPatronymic();
                    fileRow.createCell(5).setCellValue(fio);
                    fileRow.createCell(6).setCellValue("Ученик/Родитель");
                }
            } else {
                NsiDTO.Response employer = employers.stream()
                        .filter(x -> x.getGlobalId().toString().equals(record.getUserId()))
                        .findFirst().orElse(null);
                if (nonNull(employer)) {
                    String fio = employer.getSurname() + " " + employer.getFirstName();
                    if (nonNull(employer.getSecondName())) fio += " " + employer.getSecondName();
                    fileRow.createCell(5).setCellValue(fio);
                    fileRow.createCell(6).setCellValue("Сотрудник");
                }
            }
            PersonDTO person = personDTOS.stream()
                    .filter(x -> x.getPersonId().toString().equals(record.getPersonId()))
                    .findFirst().orElse(null);
            if (nonNull(person)) {
                String fio = person.getLastname() + " " + person.getFirstname();
                if (nonNull(person.getPatronymic())) fio += " " + person.getPatronymic();
                fileRow.createCell(8).setCellValue(fio);
            }
        }

        ByteArrayOutputStream fileOut = new ByteArrayOutputStream();
        workbook.write(fileOut);
        workbook.close();
        fileOut.close();
        return fileOut.toByteArray();
    }

    @SneakyThrows
    public byte[] buildErrorMessagedReport(List<ErrorMessageFileInfo> list) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("FirstSheet");

        CellStyle styleRowHead = workbook.createCellStyle();
        XSSFFont fontRowHead = workbook.createFont();
        fontRowHead.setFontName("Arial");
        fontRowHead.setFontHeightInPoints((short) 16);
        fontRowHead.setBold(true);
        styleRowHead.setFont(fontRowHead);

        XSSFRow rowHead = sheet.createRow(0);
        rowHead.createCell(0).setCellValue("№");
        rowHead.getCell(0).setCellStyle(styleRowHead);
        rowHead.createCell(1).setCellValue("Тип ошибки");
        rowHead.getCell(1).setCellStyle(styleRowHead);
        rowHead.createCell(2).setCellValue("Дата заполнения карточки");
        rowHead.getCell(2).setCellStyle(styleRowHead);
        rowHead.createCell(3).setCellValue("Дата получения сообщения");
        rowHead.getCell(3).setCellStyle(styleRowHead);
        rowHead.createCell(4).setCellValue("ФИО Оператора/Система продюсера");
        rowHead.getCell(4).setCellStyle(styleRowHead);
        rowHead.createCell(5).setCellValue("Категория данных");
        rowHead.getCell(5).setCellStyle(styleRowHead);
        rowHead.createCell(6).setCellValue("Идентификатор карточки");
        rowHead.getCell(6).setCellStyle(styleRowHead);
        rowHead.createCell(7).setCellValue("Наименование карточки");
        rowHead.getCell(7).setCellStyle(styleRowHead);
        rowHead.createCell(8).setCellValue("ФИО учащегося");
        rowHead.getCell(8).setCellStyle(styleRowHead);
        rowHead.createCell(9).setCellValue("ОО Учащегося");
        rowHead.getCell(9).setCellStyle(styleRowHead);

        Integer rowNum = 0;
        for (ErrorMessageFileInfo record : list) {
            rowNum += 1;
            XSSFRow fileRow = sheet.createRow(rowNum);
            fileRow.createCell(0).setCellValue(rowNum);
            fileRow.createCell(1).setCellValue(record.getErrorType());
            fileRow.createCell(2).setCellValue(record.getEntityCreationDate());
            fileRow.createCell(3).setCellValue(record.getCreationDate());
            if (isNull(record.getServiceCreator())) {
                fileRow.createCell(4).setCellValue(record.getEntityCreatorSurname() + " " +
                        record.getEntityCreatorFirstName() + " " +
                        record.getEntityCreatorSecondName());
            } else {
                fileRow.createCell(4).setCellValue(record.getServiceCreator());
            }
            fileRow.createCell(5).setCellValue(record.getCategoryValue());
            fileRow.createCell(6).setCellValue(record.getEntityId());
            fileRow.createCell(7).setCellValue(record.getPersonFirstName() + "" +
                    record.getPersonLastName() + " " +
                    record.getPersonPatronymic());
            fileRow.createCell(8).setCellValue(record.getPersonSchoolName());
        }

        ByteArrayOutputStream fileOut = new ByteArrayOutputStream();
        workbook.write(fileOut);
        workbook.close();
        fileOut.close();
        return fileOut.toByteArray();
    }

    private final Supplier<List<NsiDTO.Response>> nsiCache =
            Suppliers.memoizeWithExpiration(new Supplier<List<NsiDTO.Response>>() {
                public List<NsiDTO.Response> get() {
                    return nsiService.getOrgNames();
                }
            }, 1, HOURS);

    private List<NsiDTO.Response> getOrgNamesFromCache() {
        return nsiCache.get();
    }

    @SneakyThrows
    public byte[] createSecondSheet(String bearer, byte[] file, String templateConcatenation) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        AggregatedGlobalRole aggregatedGlobalRole = authService.parseRLS(tokenPayload.getRls())
                .stream().filter(x -> globalRoleEmployee.equals(x.getId())).findFirst().orElse(null);

        XSSFWorkbook workbook = new XSSFWorkbook(new ByteArrayInputStream(file));
        if (!workbook.getSheetAt(0).getDataValidations().isEmpty()) {
            workbook.getSheetAt(0).getCTWorksheet().unsetDataValidations();
        }
        XSSFSheet validationSheet = workbook.createSheet("ValidationSheet");
        XSSFSheet sheet = workbook.getSheetAt(0);
        CellStyle styleRowHead = workbook.createCellStyle();
        XSSFFont fontRowHead = workbook.createFont();
        fontRowHead.setFontName("Arial");
        fontRowHead.setFontHeightInPoints((short) 16);
        fontRowHead.setBold(true);
        styleRowHead.setFont(fontRowHead);
        int sourceColumnIndex = 0;
        if (nonNull(ExcelRefEnum.getByTemplateConcatenation(templateConcatenation))) {
            List<Triple<Class, Integer, Integer>> searchTriple = ExcelRefEnum.getByTemplateConcatenation(templateConcatenation);
            for (Triple<Class, Integer, Integer> triple : searchTriple) {
                Class clazz = triple.getLeft();
                Integer searchParam = triple.getMiddle();
                Integer targetColumnIndex = triple.getRight();
                List<String> values = new ArrayList<>();
                if (clazz.equals(EventKindRef.class)) {
                    List<EventKindRef> list = eventKindRefRepository.findAllByCategoryCodeAndIsArchive(searchParam.longValue(), false);
                    values = list.stream().sorted(Comparator.comparing(EventKindRef::getValue))
                            .map(EventKindRef::getValue).collect(Collectors.toList());
                } else if (clazz.equals(AffilationKindRef.class)) {
                    List<AffilationKindRef> list = affilationKindRefRepository.findAllByCategoryCode(searchParam.longValue());
                    values = list.stream().map(AffilationKindRef::getValue).collect(Collectors.toList());
                } else if (clazz.equals(OlympiadTypeRef.class)) {
                    List<OlympiadTypeRef> list = olympiadTypeRepository.findAll();
                    values = list.stream().sorted(Comparator.comparing(OlympiadTypeRef::getCode))
                            .map(OlympiadTypeRef::getValue).collect(Collectors.toList());
                } else if (clazz.equals(OlympiadFormatRef.class)) {
                    List<OlympiadFormatRef> list = olympiadFormatRepository.findAll();
                    values = list.stream().sorted(Comparator.comparing(OlympiadFormatRef::getCode))
                            .map(OlympiadFormatRef::getValue).collect(Collectors.toList());
                } else if (clazz.equals(SportRewardRef.class)) {
                    List<SportRewardRef> list;
                    if (nonNull(searchParam)) {
                        list = sportRewardRefRepository.findAllByCategoryCode(searchParam.longValue());
                    } else {
                        list = sportRewardRefRepository.findAll();
                    }
                    values = list.stream().sorted(Comparator.comparing(SportRewardRef::getCode))
                            .map(SportRewardRef::getValue).collect(Collectors.toList());
                } else if (clazz.equals(SubcategoryRef.class)) {
                    List<SubcategoryRef> list = subcategoryRefRepository.findAllByParentId(searchParam);
                    values = list.stream().map(SubcategoryRef::getValue).collect(Collectors.toList());
                } else if (clazz.equals(OrganizatorRef.class)) {
                    List<OrganizatorRef> list = organizatorRefRepository.findAllByIsArchive(false);
                    values = list.stream().map(OrganizatorRef::getValue).collect(Collectors.toList());
                } else if (clazz.equals(TrainingStageRef.class)) {
                    List<TrainingStageRef> list = trainingStageRefRepository.findAllByParentId(searchParam);
                    values = list.stream().sorted(Comparator.comparing(TrainingStageRef::getCode))
                            .map(TrainingStageRef::getValue).collect(Collectors.toList());
                } else if (clazz.equals(SportKindRef.class)) {
                    if (templateConcatenation.contains("3,31")) {
                        List<SportKindRef> list =
                                sportKindRefRepository.findAllByParentIdAndIsArchive(null, false);
                        values = list.stream().map(SportKindRef::getValue).collect(Collectors.toList());
                    } else {
                        List<SportKindRef> list = sportKindRefRepository.findAllByIsArchive(false);
                        values = list.stream().map(SportKindRef::getValue).collect(Collectors.toList());
                    }
                } else if (clazz.equals(TourismKindRef.class)) {
                    List<TourismKindRef> list = tourismKindRefRepository.findAllByParentId(searchParam);
                    values = list.stream().map(TourismKindRef::getValue).collect(Collectors.toList());
                } else if (clazz.equals(CreationKindRef.class)) {
                    List<CreationKindRef> list = creationKindRefRepository.findAll();
                    values = list.stream().map(CreationKindRef::getValue).collect(Collectors.toList());
                } else if (clazz.equals(SportAgeRef.class)) {
                    List<SportAgeRef> list = sportAgeRefRepository.findAllBySectionCode(searchParam);
                    values = list.stream().map(SportAgeRef::getValue).collect(Collectors.toList());
                }
                String header = sheet.getRow(0).getCell(targetColumnIndex).getStringCellValue();
                int rowNum = 0;
                XSSFRow fileRow = validationSheet.getRow(rowNum);
                if (isNull(fileRow)) {
                    fileRow = validationSheet.createRow(rowNum);
                }
                fileRow.createCell(sourceColumnIndex).setCellValue(header);
                for (String value : values) {
                    rowNum++;
                    fileRow = validationSheet.getRow(rowNum);
                    if (isNull(fileRow)) {
                        fileRow = validationSheet.createRow(rowNum);
                    }
                    fileRow.createCell(sourceColumnIndex).setCellValue(value);
                }
                addDataValidation(targetColumnIndex, sourceColumnIndex, rowNum + 1, workbook, sheet, validationSheet);
                sourceColumnIndex++;
            }
        }
        List<NsiDTO.Response> nsiResponse = getOrgNamesFromCache();
        if (Objects.nonNull(aggregatedGlobalRole) && !CollectionUtils.isEmpty(aggregatedGlobalRole.getLocalRoles())) {
            List<AggregatedGlobalRole.AggregatedLocalRole> ooEmployees =
                    aggregatedGlobalRole.getLocalRoles().stream()
                            .filter(x -> !LongStream.range(32, 40).boxed().collect(Collectors.toList()).contains(x.getId()))
                            .filter(x -> Lists.newArrayList(adminOO, employeeOO, teacherOO).contains(x.getId()))
                            .collect(Collectors.toList());
            if (!ooEmployees.isEmpty()) {
                nsiResponse = nsiResponse.stream().filter(x -> ooEmployees.stream()
                                .map(AggregatedGlobalRole.AggregatedLocalRole::getOrgIds).flatMap(Collection::stream)
                                .collect(Collectors.toList()).contains(x.getGlobalId().intValue()))
                        .collect(Collectors.toList());
            }
        }
        List<String> schoolNames = nsiResponse.stream().map(x -> {
            if (nonNull(x.getShortName())) {
                return x.getShortName();
            } else return x.getFullName();
        }).collect(Collectors.toList());
        int rowNum = 0;
        XSSFRow fileRow = validationSheet.getRow(rowNum);
        if (isNull(fileRow)) {
            fileRow = validationSheet.createRow(rowNum);
        }
        fileRow.createCell(sourceColumnIndex).setCellValue("Школа");
        for (String value : schoolNames) {
            rowNum++;
            fileRow = validationSheet.getRow(rowNum);
            if (isNull(fileRow)) {
                fileRow = validationSheet.createRow(rowNum);
            }
            fileRow.createCell(sourceColumnIndex).setCellValue(value);
        }
        addDataValidation(7, sourceColumnIndex, rowNum + 1, workbook, sheet, validationSheet);

        ByteArrayOutputStream fileOut = new ByteArrayOutputStream();
        workbook.write(fileOut);
        workbook.close();
        fileOut.close();
        return fileOut.toByteArray();
    }

    private void addDataValidation(Integer targetColumnIndex, Integer sourceColumnIndex, Integer lastRowNum,
                                   XSSFWorkbook workbook, XSSFSheet sheet, XSSFSheet validationSheet) {
        XSSFName name = workbook.createName();
        name.setNameName("valid" + (sourceColumnIndex + 1));
        String sourceColumnIndexStr;
        if (sourceColumnIndex < 35) {
            sourceColumnIndexStr = Character.toString((char) (65 + sourceColumnIndex));
        } else {
            sourceColumnIndexStr = "A" + (char) (65 + sourceColumnIndex);
        }
        name.setRefersToFormula("ValidationSheet!$" + sourceColumnIndexStr + "$2:$" + sourceColumnIndexStr + "$" + lastRowNum);
        int lastRow = workbook.getSpreadsheetVersion().getLastRowIndex();
        XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(validationSheet);
        XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper.createFormulaListConstraint(name.getNameName());
        CellRangeAddressList addressList = new CellRangeAddressList(1, lastRow, targetColumnIndex, targetColumnIndex);
        XSSFDataValidation validation = (XSSFDataValidation) dvHelper.createValidation(dvConstraint, addressList);
        validation.setSuppressDropDownArrow(true);
        validation.setShowErrorBox(true);
        sheet.addValidationData(validation);
    }

    @SneakyThrows
    public byte[] buildAdministrationErrorRecordReport(List<AdministrationErrorRecordRow> list) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("FirstSheet");

        CellStyle styleRowHead = workbook.createCellStyle();
        XSSFFont fontRowHead = workbook.createFont();
        fontRowHead.setFontName("Arial");
        fontRowHead.setFontHeightInPoints((short) 16);
        fontRowHead.setBold(true);
        styleRowHead.setFont(fontRowHead);

        XSSFRow rowHead = sheet.createRow(0);
        rowHead.createCell(0).setCellValue("Идентификатор карточки Портфолио");
        rowHead.getCell(0).setCellStyle(styleRowHead);
        rowHead.createCell(1).setCellValue("Дата сообщения об ошибке");
        rowHead.getCell(1).setCellStyle(styleRowHead);
        rowHead.createCell(2).setCellValue("Категория данных");
        rowHead.getCell(2).setCellStyle(styleRowHead);
        rowHead.createCell(3).setCellValue("Наименование достижения");
        rowHead.getCell(3).setCellStyle(styleRowHead);
        rowHead.createCell(4).setCellValue("Тип жалобы");
        rowHead.getCell(4).setCellStyle(styleRowHead);
        rowHead.createCell(5).setCellValue("Раздел карточки");
        rowHead.getCell(5).setCellStyle(styleRowHead);
        rowHead.createCell(6).setCellValue("Дата заполнения карточки");
        rowHead.getCell(6).setCellStyle(styleRowHead);
        rowHead.createCell(7).setCellValue("Комментарий от пользователя");
        rowHead.getCell(7).setCellStyle(styleRowHead);
        rowHead.createCell(8).setCellValue("Оператор");
        rowHead.getCell(8).setCellStyle(styleRowHead);
        rowHead.createCell(9).setCellValue("ФИО учащегося");
        rowHead.getCell(9).setCellStyle(styleRowHead);
        rowHead.createCell(10).setCellValue("СНИЛС учащегося");
        rowHead.getCell(10).setCellStyle(styleRowHead);
        rowHead.createCell(11).setCellValue("Дата рождения учащегося");
        rowHead.getCell(11).setCellStyle(styleRowHead);
        rowHead.createCell(12).setCellValue("Класс учащегося");
        rowHead.getCell(12).setCellStyle(styleRowHead);
        rowHead.createCell(13).setCellValue("ОО учащегося");
        rowHead.getCell(13).setCellStyle(styleRowHead);
        rowHead.createCell(14).setCellValue("Автор сообщения об ошибке");
        rowHead.getCell(14).setCellStyle(styleRowHead);
        int rowNum = 0;
        for (AdministrationErrorRecordRow record : list) {
            rowNum += 1;
            XSSFRow fileRow = sheet.createRow(rowNum);
            fileRow.createCell(0).setCellValue(record.getId());
            fileRow.createCell(1).setCellValue(record.getCreationDate().toString());
            fileRow.createCell(2).setCellValue(record.getCategory());
            fileRow.createCell(3).setCellValue(record.getEntityName());
            fileRow.createCell(4).setCellValue(record.getErrorType());
            fileRow.createCell(5).setCellValue(record.getChapter());
            if (nonNull(record.getEntityCreationDate())) {
                fileRow.createCell(6).setCellValue(record.getEntityCreationDate().toString());
            }
            fileRow.createCell(7).setCellValue(record.getComment());
            fileRow.createCell(8).setCellValue(record.getOperator());
            fileRow.createCell(9).setCellValue(record.getFio());
            fileRow.createCell(10).setCellValue(record.getSnils());
            fileRow.createCell(11).setCellValue(record.getBirthDate());
            fileRow.createCell(12).setCellValue(record.getClassName());
            fileRow.createCell(13).setCellValue(record.getSchoolName());
            fileRow.createCell(14).setCellValue(record.getErrorMessageAuthor());
        }
        ByteArrayOutputStream fileOut = new ByteArrayOutputStream();
        workbook.write(fileOut);
        workbook.close();
        fileOut.close();
        return fileOut.toByteArray();
    }
}
