package ru.portfolio.ax.util.security;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.CastUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import ru.portfolio.ax.model.ShareLink;
import ru.portfolio.ax.model.User;
import ru.portfolio.ax.model.common.PersonallyEntity;
import ru.portfolio.ax.repository.ShareLinkRepository;
import ru.portfolio.ax.repository.UserRepository;
import ru.portfolio.ax.rest.dto.AuthPermission;
import ru.portfolio.ax.rest.dto.PersonInfoDTO;
import ru.portfolio.ax.rest.dto.aupd.AccessTokenPayloadDto;
import ru.portfolio.ax.rest.dto.aupd.CurrentUserRolesDTO;
import ru.portfolio.ax.rest.exception.PortfolioAuthorizationException;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.service.ClickHouseService;
import ru.portfolio.ax.service.DataService;
import ru.portfolio.ax.service.ext.AUPDService;
import ru.portfolio.ax.util.Utils;

import javax.annotation.PostConstruct;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.google.common.collect.ImmutableSet.of;
import static ru.portfolio.ax.rest.exception.PortfolioCodifiedEnum.E565;
import static ru.portfolio.ax.util.Utils.safeGet;
import static ru.portfolio.ax.util.Utils.safetyGet;

@Slf4j
@Component
@RequiredArgsConstructor
public class AuthComponent {
    private final UserRepository userRepository;
    private final ShareLinkRepository shareLinkRepository;
    private final ObjectMapper objectMapper;
    private final AUPDService aupdService;
    private final AuthService authService;
    private final DataService service;
    private final ClickHouseService clickHouseService;

    private final Map<String, String> operatorProperty;
    private final Map<String, String> operatorEntityProperty;
    private final Map<String, String> operatorCategoryProperty;
    private Map<Long, Pair<Set<String>, Set<Long>>> operator2class2category = new HashMap<>();

    @Value("${aupd.subsystemId}")
    private String subsystemId;

    @Value("${aupd.issuer}")
    private String tokenIssuer;
    @Value("${roles.global.employeeId}")
    private Long roleEmployeeId;
    @Value("${roles.global.adminId}")
    private Long roleAdminId;
    @Value("${roles.global.parentId}")
    private Long roleParentId;
    @Value("${roles.global.childId}")
    private Long roleStudentId;

    private Map<Secured.GlobalRole, Set<Long>> role2id = Collections.emptyMap();

    @PostConstruct
    private void init() {
        for (String key : operatorProperty.keySet()) {
            operator2class2category.put(NumberUtils.createLong(operatorProperty.get(key)),
                    Pair.of(Utils.split(operatorEntityProperty.get(key)),
                            Utils.splitToIds(operatorCategoryProperty.get(key)))
            );
        }

        role2id = ImmutableMap.of(
                Secured.GlobalRole.ALL, of(roleStudentId, roleEmployeeId, roleAdminId, roleParentId),
                Secured.GlobalRole.EMPLOYEE, Collections.singleton(roleEmployeeId),
                Secured.GlobalRole.STUDENT, Collections.singleton(roleStudentId),
                Secured.GlobalRole.AGENT, Collections.singleton(roleParentId),
                Secured.GlobalRole.ADMIN, Collections.singleton(roleAdminId)
        );
    }

    public void userAuth(Secured.Secure secured, String method, String personId) {
        userAuth(secured, personId, method, null);
    }

    public void userAuthBearer(Secured.Secure secured, String method) {
        userAuth(secured, null, method, null);
    }

    public void userAuth(Secured.Secure secured, String personId, String method, Object forCreate) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String authToken = StringUtils.substringAfter(request.getHeader("Authorization"), "Bearer ");
        userAuth(secured, personId, method, forCreate, authToken, request);
    }

    public void userAuth(Secured.Secure secured, String personId, String method,
                         Object forCreate, String authToken, HttpServletRequest request) {
//        if ("getAll".equals(method) && authToken != null) {
//            ;
//        }
        if ("create".equals(method)) {
            Preconditions.checkNotNull(forCreate);
        }

        log.info("New UserAuthorization");

        // todo uncomment for simplify stream code
        //  String authToken = request.getHeader("authorization");

        // todo comment
        //Object[] args = jp.getArgs();

        if (secured.urlCookie && Objects.nonNull(request.getCookies())) {
            Cookie share = Utils.first(Arrays.asList(request.getCookies()), c -> c.getName().equals("SHARE"));

            //MethodSignature methodSig = CastUtils.cast(jp.getSignature());
            //String personId = getPersonIdFromArgs(methodSig.getParameterNames(), args);

            boolean containedAndValid = containValidUrlCookie(Utils.safeGet(share, Cookie::getValue), personId);
            log.info("User Authorized");
            if (containedAndValid) return;
        }
        authCheck(secured, authToken, method);

        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(authToken);
        Cookie aud = safetyGet(() ->
                Utils.first(Arrays.asList(request.getCookies()), c -> c.getName().equals("aupd_current_role")));
        PortfolioException.check(Objects.nonNull(aud),
                PortfolioException.get442("aupd_current_role"));
        tokenPayload.setAud(Utils.safeGet(aud, Cookie::getValue));
        if (secured.byPerson) {
            checkPermissions(personId, method, secured, tokenPayload);
        } else {
            if (method.equals("create")) {
                checkForOperatorNabivator(forCreate, secured, tokenPayload);
            }
        }

        log.info("User Authorized");
    }

    @SneakyThrows
    private void checkPermissions(String personId, String method, Secured.Secure secured, AccessTokenPayloadDto tokenPayload) {
        if (!secured.byPerson) return;
        if (method.equals("initContext") ||
                method.equals("createSubsystemLink") || method.equals("generateFosLink")) {
            return;
        }
        log.info("Check user permissions");
        // MethodSignature methodSig = CastUtils.cast(jp.getSignature());

        // 1
        //AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(authToken);
        // BUILD METHOD REQUEST MAP
        // Object[] args = jp.getArgs();
        // 2
        // String personId = getPersonIdFromArgs(methodSig.getParameterNames(), args);
        /*if (Objects.isNull(personId))
            for (Object arg : args) {
                if (AuthPermission.class.isAssignableFrom(arg.getClass())) {
                    AuthPermission cast = CastUtils.cast(arg);
                    personId = cast.getPersonId();
                    break;
                }
            }*/
        PortfolioException.check(Objects.nonNull(personId), PortfolioException.get566());
        Collection<String> personIds = clickHouseService.getPersonIds(Objects.requireNonNull(personId));
        // 3 fixme NPE
        User user = userRepository.findFirstByAupdId(tokenPayload.getSub()).orElse(null);
        CurrentUserRolesDTO currentUserRolesDTO = objectMapper.convertValue(user.getCurrentUserRoles(), CurrentUserRolesDTO.class);
        if (currentUserRolesDTO.getCurrentMeshRoleId().equals(roleParentId)) {
            if (personIds.stream().noneMatch(user.getChildren()::contains)) {
                throw PortfolioException.get562();
            }
        } else if (currentUserRolesDTO.getCurrentMeshRoleId().equals(roleStudentId)) {
            if (!personIds.contains(tokenPayload.getMsh())) {
                throw PortfolioException.get562();
            }
        }
        log.info("User permissions confirmed");
    }

    private void authCheck(Secured.Secure secured, String authToken, String method) {
        log.info("AUPD Authorization start");
        PortfolioException.check(Objects.nonNull(authToken), PortfolioException.get561());
        // 1
        authService.checkValidToken(Objects.requireNonNull(authToken));
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(authToken);

        // 2
        int offset = 5 * 60;
        Instant expirationDate = Instant.ofEpochSecond(Long.parseLong(tokenPayload.getExp()));
        boolean expSp1583 = Instant.now().isBefore(expirationDate.plusSeconds(offset));
        PortfolioException.check(expSp1583, PortfolioException.get557(expirationDate.toString()));

        // 3
        Instant startDate = Instant.ofEpochSecond(Long.parseLong(tokenPayload.getNbf()));
        boolean nbfSp1583 = Instant.now().isAfter(startDate.minusSeconds(offset));
        PortfolioException.check(nbfSp1583, PortfolioException.get558(startDate.toString()));
        // 4
        //  PortfolioException.check(tokenPayload.getIss().equals(tokenIssuer), PortfolioException.get559());
        // 5
        User user = userRepository.findFirstByAupdId(tokenPayload.getSub()).orElse(null);
//        boolean newUser = Objects.isNull(user);
        // 5.1
//        if (newUser) {
//            user = new User();
//            user.setAupdId(tokenPayload.getSub());
//            userRepository.save(user);
//        }
        PortfolioException.check(Objects.nonNull(user), PortfolioException.get469());
        PortfolioException.check(Objects.nonNull(user.getCurrentUserRoles()), PortfolioException.get470());

        boolean getCurrentRoles = method.equals("initContext");
        // 5.2
        if (Objects.isNull(user.getCurrentUserRoles()) && !getCurrentRoles) {
            //5.2.2
            CurrentUserRolesDTO currentUserRolesGettingResponse = aupdService.getCurrentUserRoles(authToken, tokenPayload.getSub());
            PortfolioException.check(!BooleanUtils.isFalse(currentUserRolesGettingResponse.getCurrentUserRolesGettingResult()), PortfolioException.get555());
            //5.2.3
            Long meshRoleId = Utils.safeGet(currentUserRolesGettingResponse, CurrentUserRolesDTO::getCurrentMeshRoleId);
            Set<Long> globalRoles = Stream.of(secured.globalRoles)
                    .map(role2id::get).flatMap(Set::stream).collect(Collectors.toSet());
            PortfolioException.check(globalRoles.contains(meshRoleId), PortfolioException.get559());

            //5.3
            JsonNode node = objectMapper.convertValue(currentUserRolesGettingResponse, JsonNode.class);
            user.setCurrentUserRoles(node);
            userRepository.save(user);
            // 5.4
//            Long meshRoleId = safeGet(currentUserRolesGettingResponse, CurrentUserRolesDTO::getCurrentMeshRoleId);
//            PortfolioException.check(Objects.nonNull(meshRoleId) && ArrayUtils.contains(secured.globalRoles(), meshRoleId),
//                    PortfolioException.get559());
            // 5.5
            boolean checkMash = currentUserRolesGettingResponse.getCurrentMeshRoleId().equals(roleStudentId) ||
                    currentUserRolesGettingResponse.getCurrentMeshRoleId().equals(roleParentId);
            PersonInfoDTO personMeshInfo;
            if (Objects.nonNull(tokenPayload.getMsh()) && checkMash) {
                try {
                    personMeshInfo = service.getPersonInfo(tokenPayload.getMsh(), null, false);
                } catch (Exception ex) {
                    throw PortfolioException.get439();
                }
                PortfolioException.check(Objects.nonNull(personMeshInfo), PortfolioException.get439());
                if (currentUserRolesGettingResponse.getCurrentMeshRoleId().equals(roleParentId)) {
                    if (Objects.nonNull(personMeshInfo.getPupils()) && !personMeshInfo.getPupils().isEmpty()) {
                        try {
                            List<PersonInfoDTO> children = personMeshInfo.getPupils().stream().map(x -> service.getPersonInfo(x.toString(), null, false)).collect(Collectors.toList());
                            user.setChildren(StringUtils.join(children.stream().filter(Objects::nonNull).map(PersonInfoDTO::getPersonId).collect(Collectors.toSet())));
                            userRepository.save(user);
                        } catch (Exception ex) {
                            throw PortfolioException.get440();
                        }
                    }
                }
            }
        }

        // 6.1
        if (Objects.nonNull(user.getCurrentUserRoles()) && !getCurrentRoles) {
            CurrentUserRolesDTO currentUserRolesDTO = objectMapper.convertValue(user.getCurrentUserRoles(), CurrentUserRolesDTO.class);
            Long meshRoleId = Utils.safeGet(currentUserRolesDTO, CurrentUserRolesDTO::getCurrentMeshRoleId);
            Set<Long> globalRoles = Stream.of(secured.globalRoles)
                    .map(role2id::get).flatMap(Set::stream).collect(Collectors.toSet());
            PortfolioException.check(globalRoles.contains(meshRoleId), PortfolioException.get559());
        }
        log.info("AUPD Access granted");
    }

    @SneakyThrows
    private boolean containValidUrlCookie(String urlCookie, String personIdArg) {
        if (StringUtils.isEmpty(urlCookie)) return false;
        byte[] base64 = Base64.getDecoder().decode(urlCookie);
        ShareLink shareLinkQuery = objectMapper.readValue(base64, ShareLink.class);
        shareLinkQuery.setIsActive(Boolean.TRUE);

        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("UTC"));
        Optional<ShareLink> link = Optional.ofNullable(shareLinkRepository.findByUrlAndIsActive(shareLinkQuery.getUrl(), true));

        PortfolioException portfolioException = PortfolioException.get(E565);
        ShareLink shareLink = link.orElseThrow(() -> portfolioException);
        PortfolioException.check(shareLink.getPersonId().equals(personIdArg), portfolioException);
        PortfolioException.check(!now.isBefore(shareLink.getStartDate()) || now.isEqual(shareLink.getStartDate()), portfolioException);
        PortfolioException.check(!now.isAfter(shareLink.getEndDate()) || now.isEqual(shareLink.getEndDate()), portfolioException);

        return true;
    }

    private void checkForOperatorNabivator(Object forCreate, Secured.Secure secured, AccessTokenPayloadDto tokenPayload) {
        // if (secured.byPerson() || args.length != 1) return;
        if (!PersonallyEntity.class.isAssignableFrom(forCreate.getClass())) return;
        PersonallyEntity entity = CastUtils.cast(forCreate);
        PortfolioAuthorizationException error = PortfolioException.get559();

        //AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(authToken);

        String wrap = StringUtils.wrap(subsystemId, ':');
        List<String> filtered = Utils.transform(Arrays.asList(StringUtils.split(
                tokenPayload.getRls(), ',')),
                t -> convert(t, wrap), StringUtils::isNoneEmpty);

        Set<Long> localRoles = Utils.extract(filtered, NumberUtils::createLong);
        PortfolioException.check(CollectionUtils.isNotEmpty(filtered), error);
        PortfolioException.check(CollectionUtils.containsAny(operator2class2category.keySet(), localRoles), error);

        List<Pair<Set<String>, Set<Long>>> pairs = new ArrayList<>();
        for (Long localRole : localRoles) {
            Pair<Set<String>, Set<Long>> pair = operator2class2category.get(localRole);
            if (Objects.nonNull(pair)) pairs.add(pair);
        }
        PortfolioException.check(!pairs.isEmpty(), error);

        boolean check = false;
        for (Pair<Set<String>, Set<Long>> p : pairs) {
            if (p.getFirst().isEmpty() || p.getSecond().isEmpty()) {
                return;
            }
            if (p.getFirst().contains(entity.getClass().getSimpleName()) &&
                    p.getSecond().contains(entity.getCategoryCode().longValue())) {
                check = true;
            }
        }
        PortfolioException.check(check, error);
    }

    private String convert(String t, String system) {
        int i = StringUtils.indexOf(t, system);
        if (i < 0) return null;
        String sub = t.substring(0, i);
        if (NumberUtils.isDigits(sub)) return sub;
        return StringUtils.substringBetween(t, "[", system);
    }

    public String getPersonIdFromArgs(String[] paramNames, Object[] args) {
        Map<String, Object> map = IntStream.range(0, paramNames.length).boxed()
                .collect(Collectors.toMap(i -> paramNames[i], i -> ObjectUtils.defaultIfNull(args[i], "")));
        String personId = safeGet(map.get("personId"), Object::toString);
        if (StringUtils.isEmpty(personId)) {
            List<Object> collect = map.values().stream().filter(i -> i instanceof AuthPermission).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                AuthPermission authObject = (AuthPermission) collect.get(0);
                personId = authObject.getPersonId();
            }
        }
        return personId;
    }
}
