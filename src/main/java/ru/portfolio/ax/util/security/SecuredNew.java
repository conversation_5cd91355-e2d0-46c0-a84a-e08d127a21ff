package ru.portfolio.ax.util.security;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.List;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SecuredNew {
    GlobalRole[] globalRoles();

    boolean urlCookie() default false;

    boolean byPerson() default true;

    enum GlobalRole {
        STUDENT, // Учащийся
        AGENT, // Родитель/Представить
        OPERATOR, // Сотрудник
        ADMIN, // Администратор
        HEAD_TEACHER,
        ADMIN_O_O,
        TEACHER
    }

    enum LocalRole {
        STUDENT_OPERATOR,
        AGENT_OPERATOR,
        GLOBAL_OPERATOR,
        STUDY_OPERATOR,
        SCIENCE_OPERATOR,
        SPORT_OPERATOR,
        CREATION_OPERATOR,
        CULTURE_OPERATOR,
        CIVIL_OPERATOR,
        PROFESSION_OPERATOR,
        EMPLOYEE_O_O,
        ADMIN_O_O,
        TEACHER_O_O,
        ADMIN_LOCAL_ROLE,
        ADMIN_R_O
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Secure {
        List<GlobalRole> globalRoles;
        boolean urlCookie;
        boolean byPerson;

        /*public static SecuredNew.Secure of(SecuredNew secured) {
            SecuredNew.Secure secure = new SecuredNew.Secure();
            secure.byPerson = secured.byPerson();
            secure.urlCookie = secured.urlCookie();
            secure.globalRoles = secured.globalRoles()[0];//todo
            return secure;
        }*/

        public static SecuredNew.Secure standard(List<GlobalRole> globalRoles) {
            SecuredNew.Secure secure = new SecuredNew.Secure();
            secure.byPerson = true;
            secure.urlCookie = false;
            secure.setGlobalRoles(globalRoles);//todo
            return secure;
        }

        public static SecuredNew.Secure byCookie(List<GlobalRole> globalRoles) {
            SecuredNew.Secure secure = new SecuredNew.Secure();
            secure.byPerson = true;
            secure.urlCookie = true;
            secure.setGlobalRoles(globalRoles);//todo
            return secure;
        }

        public static SecuredNew.Secure notByPerson(List<GlobalRole> globalRoles) {
            SecuredNew.Secure secure = new SecuredNew.Secure();
            secure.byPerson = false;
            secure.urlCookie = false;
            secure.setGlobalRoles(globalRoles);//todo
            return secure;
        }

        public static SecuredNew.Secure byCookieNotByPerson(List<GlobalRole> globalRoles) {
            SecuredNew.Secure secure = new SecuredNew.Secure();
            secure.byPerson = false;
            secure.urlCookie = true;
            secure.setGlobalRoles(globalRoles);//todo
            return secure;
        }
    }
}
