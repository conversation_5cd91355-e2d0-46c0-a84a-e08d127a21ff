package ru.portfolio.ax.rest.dto.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@RequiredArgsConstructor
public enum MarkEventType {
    Урок("Урок", 1),
    Домашнее_задание("Домашнее задание", 2),
    Контрольная_работа("Контрольная работа", 3),
    Аттестация("Аттестация", 4);

    private final String name;
    private final Integer id;

    public static List<String> getNames() {
        return Stream.of(MarkEventType.values()).map(MarkEventType::getName).collect(Collectors.toList());
    }
}
