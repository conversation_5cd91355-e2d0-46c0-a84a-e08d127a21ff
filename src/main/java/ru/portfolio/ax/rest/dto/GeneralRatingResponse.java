package ru.portfolio.ax.rest.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.List;

@Data
@NoArgsConstructor
public class GeneralRatingResponse {
    private String learningYear;
    private List<GeneralRating> generalRating;

    @Data
    @NoArgsConstructor
    public static class GeneralRating {
        private String subjectGeneralRatingName;
        private GeneralRatingEntity generalRatingRegion;
        private GeneralRatingEntity generalRatingSchool;
        private GeneralRatingEntity generalRatingClass;
    }

    @Data
    @NoArgsConstructor
    public static class GeneralRatingEntity {
        private Double averageResultPercentStudent;
        private Double percentLowerOthers;
        private Integer placeGeneralRatingStudent;
        private Double averageResultPercentFirstPlace;
        private Double averageResultPercentSecondPlace;
        private Double averageResultPercentThirdPlace;
        private Double averageResultPercentPenultimatePlace;
        private Double averageResultPercentLastPlace;
        private Integer placeGeneralRatingPenultimatePlace;
        private Integer placeGeneralRatingLastPlace;
        private List<Double> allPlaces = new LinkedList<>();
    }
}
