package ru.portfolio.ax.rest.dto.contingent;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AgentsDTO {
    @JsonProperty("agent_person_id")
    private String agentId;
    @JsonProperty("agent_person")
    private Agent<PERSON>erson agentPerson;

    @Data
    public static class AgentPerson {
        private String lastname;
        private String firstname;
        private String patronymic;
    }
}
