package ru.portfolio.ax.rest.dto.contingent;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Sets;
import lombok.Data;
import ru.portfolio.ax.util.Utils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.math.NumberUtils.INTEGER_ONE;
import static org.apache.commons.lang3.math.NumberUtils.INTEGER_TWO;

@Data
public class PersonDTO {
    private Long id;
    @JsonProperty("person_id")
    private UUID personId;
    private String lastname;
    private String firstname;
    private String patronymic;
    private String birthdate;
    private String snils;

    private Integer gender_id;

    private List<EducationDTO> education;
    private List<ContactDTO> contacts;
    private List<JsonNode> documents;
    private List<AgentsDTO> agents;
    @JsonIgnore
    private List<CategoriesDTO> categories;
    private List<ChildrenDTO> children;

    public String getPersonClass() {
        ClassDTO classDTO = Utils.extractFirst(education, EducationDTO::getClassDTO);
        return Utils.safeGet(classDTO, ClassDTO::getName);
    }

    public String getTelephone() {
        Predicate<ContactDTO> predicate = contactDTO -> Objects.equals(contactDTO.getTypeId(), INTEGER_ONE);
        return Utils.extractFirst(contacts, predicate, ContactDTO::getData);
    }

    public String getEmail() {
        Predicate<ContactDTO> predicate = contactDTO -> Objects.equals(contactDTO.getTypeId(), INTEGER_ONE + INTEGER_TWO);
        return Utils.extractFirst(contacts, predicate, ContactDTO::getData);
    }

    public String getClassLevel() {
        ClassDTO classDTO = Utils.extractFirst(education, EducationDTO::getClassDTO);
        return Utils.safeGet(classDTO, ClassDTO::getParallelName);
    }

    public boolean containsStages() {//fixme mb findFirst!
        return !getEducationByContainsStages().isEmpty();
    }

    public List<EducationDTO> getEducationByContainsStages() {
        Set<Integer> set = Sets.newHashSet(1, 2, 3, 4, 5);
        return Utils.filter(education, e -> set.contains(e.getStageId()));
    }

    public List<String> getCategoryParameterValue(Integer category) {
        return Utils.extractFirst(categories, categories -> categories.getCategoryId().equals(category), CategoriesDTO::getParameterValues);
    }
}
