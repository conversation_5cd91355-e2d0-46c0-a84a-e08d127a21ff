package ru.portfolio.ax.rest.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PersonInfoDTO implements AbstractDTO {

    @JsonProperty("meshId")
    private UUID personId;
    private List<String> oldContingentId;

    @JsonProperty("class")
    private String personClass;
    private String classLevel;

    private String schoolName;
    private Integer schoolId;

    private String lastname;
    private String firstname;
    private String patronymic;

    private String email;
    private String telephone;
    private String birthdate;

    private Integer gender;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<UUID> pupils;

    private Boolean studentData;

    private String classId;
    private List<Integer> staffIds = new ArrayList<>();
    private String snils;
    private String schoolOKTMO;
}
