package ru.portfolio.ax.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LinkShowArea implements AbstractDTO {
    private Boolean studentData;
    private Boolean studies;
    private Boolean diagnostic;
    private Boolean science;
    private Boolean sport;
    private Boolean creation;
    private Boolean culture;
    private Boolean civil;

    private Boolean olympiads;
    private Boolean sportGames;
    private Boolean hike;
    private Boolean creationContest;
    private Boolean civilContest;
    private Boolean offlineVisit;
    private Boolean onlineVisit;
    private Boolean scienceContest;
    private Boolean performance;
    private Boolean interests;
    private Boolean profile;

    private Boolean profession;
    private Boolean professionRewards;
    private Boolean professionEducation;
    private Boolean professionEvents;
    private Boolean professionWorldskills;

    private Boolean sportUnit;
    private Boolean creationUnit;
    private Boolean civilUnit;
    private Boolean scienceEmployments;

    private Boolean sportReward;
    private Boolean creationReward;
    private Boolean civilReward;
    private Boolean scienceReward;

    private Boolean sportClub;
    private Boolean creationClub;
    private Boolean civilClub;

    private Boolean projects;

    private Boolean gia;
    private Boolean oge;
    private Boolean ege;
    private Boolean gve9;
    private Boolean gve11;
    private Boolean other;

    private Boolean selfDiagnostic;
    private Boolean independentDiagnostic;
    private Boolean personalDiagnostic;

    private Boolean practice;
    private Boolean trainingInfo;
    private Boolean documents;
    private Boolean job;
    private Boolean metaskills;

    private Boolean professionCareerGuidance;
    private Boolean professionRecommendations;
    private Boolean professionProfTests;

}
