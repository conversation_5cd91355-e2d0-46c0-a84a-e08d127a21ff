package ru.portfolio.ax.rest.dto.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum EventType {
    Иное("Иное"),
    Проход("Проход"),
    Пи<PERSON><PERSON><PERSON><PERSON><PERSON>("Питани<PERSON>"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Оценка"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Цифровой тренинг для самоподготовки"),
    Ци<PERSON>рова<PERSON>("Цифровая контрольная работа"),
    Самодиагностика("Самодиагностика"),
    Олимпиада("Олимпиада"),
    ГИА("ГИА"),
    Библиотека("Библиотека МЭШ"),
    Расписание("Расписание уроков"),
    Отсутствие("Отсутствие на уроке"),
    <PERSON>ро<PERSON><PERSON><PERSON><PERSON>("Профиль обучения"),
    Урок("Урок"),
    Посещение("Посещение культурного учреждения"),
    Достижение("Достижение");

    private final String name;
}
