package ru.portfolio.ax.rest.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class IndependentDiagnosticByPersonDTO {
    private String personId;
    private Integer diagnosticWorkId;
    private String diagnosticSubjectName;
    private Integer diagnosticMaxExecutionResult;
    private Integer diagnosticExecutionResultMarkValue;
    private Double diagnosticPercentExecutionResult;
    private String learningYear;
    private String schoolId;
}
