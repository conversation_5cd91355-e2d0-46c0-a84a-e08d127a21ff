package ru.portfolio.ax.rest.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DeleteCardDTO {
    private String personId;
    private Integer categoryCode;
    private String entityName;
    private String entityType;
    private Long entityId;
    private LocalDateTime creationDate;
    private Integer dataSourceCode;
    private String creatorId;
    private Boolean isActual;
    private FioDTO student;
    private FioDTO creator;
    private Integer dataKind;
}
