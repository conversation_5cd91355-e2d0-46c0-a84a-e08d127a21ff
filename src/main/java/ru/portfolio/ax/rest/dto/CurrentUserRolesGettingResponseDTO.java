package ru.portfolio.ax.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import ru.portfolio.ax.rest.dto.aupd.CurrentUserRolesDTO;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CurrentUserRolesGettingResponseDTO {

    private CurrentUserRolesDTO CurrentUserRolesGettingResponse;
    private String stf;
    private String msh;
    private String sso;
    private Long sub;
}
