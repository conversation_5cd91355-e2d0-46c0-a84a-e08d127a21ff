package ru.portfolio.ax.rest.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.util.Pair;
import ru.portfolio.ax.model.dto.IndependentDiagnosticDTO;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class GetIndependentDiagnosticsByLearningYearResponse {

    private String learningYear;
    private Diagnostic diagnostics;

    @Data
    @NoArgsConstructor
    private class Diagnostic {
        private List<IndependentDiagnosticDTO> diagnostic;
        private TotalDiagnostic totalDiagnostic;
        private BestResult bestResult;
        private List<AverageResultPercent> averageResultPercent = new ArrayList<>();
    }

    @Data
    @NoArgsConstructor
    private class TotalDiagnostic {
        private Long total;
        private TotalLevelType totalLevelType;
    }

    @Data
    @NoArgsConstructor
    private class TotalLevelType {
        private Long totalLevelBelowBase;
        private Long totalLevelBase;
        private Long totalLevelHight;
        private Long totalLevelOverHight;
    }

    @Data
    @NoArgsConstructor
    private class BestResult {
        private Double bestResultPercent;
        private Integer bestResultValue;
        private Integer maxResultValue;
        private LocalDate bestResultEventDate;
        private String bestResultLevelType;
        private String bestResultSubject;
        private Short markValue5;
    }

    @Data
    @NoArgsConstructor
    private class AverageResultPercent {
        private Double resultPercent;
        private String subject;
    }

    public void fillDiagnostic(String learningYear,
                               List<IndependentDiagnosticDTO> selfDiagnostics,
                               Long total,
                               Long totalLevelBelowBase,
                               Long totalLevelBase,
                               Long totalLevelHight,
                               Long totalLevelOverHight,
                               Double bestResultPercent,
                               String bestResultSubject,
                               Integer bestResultValue,
                               Integer bestMaxResult,
                               LocalDate bestEventDate,
                               String bestLevelType,
                               Short markValue5,
                               List<Pair<String, Double>> averageResults) {
        Diagnostic diag = new Diagnostic();

        TotalLevelType totalLevelType = new TotalLevelType();
        totalLevelType.setTotalLevelBelowBase(totalLevelBelowBase);
        totalLevelType.setTotalLevelBase(totalLevelBase);
        totalLevelType.setTotalLevelHight(totalLevelHight);
        totalLevelType.setTotalLevelOverHight(totalLevelOverHight);

        TotalDiagnostic totalDiagnostic = new TotalDiagnostic();
        totalDiagnostic.setTotal(total);
        totalDiagnostic.setTotalLevelType(totalLevelType);

        BestResult bestResult = new BestResult();
        bestResult.setBestResultPercent(bestResultPercent);
        bestResult.setBestResultSubject(bestResultSubject);
        bestResult.setBestResultValue(bestResultValue);
        bestResult.setMaxResultValue(bestMaxResult);
        bestResult.setBestResultEventDate(bestEventDate);
        bestResult.setBestResultLevelType(bestLevelType);
        bestResult.setMarkValue5(markValue5);

        diag.setDiagnostic(selfDiagnostics);
        diag.setBestResult(bestResult);
        diag.setTotalDiagnostic(totalDiagnostic);

        for (Pair<String, Double> pair : averageResults) {
            AverageResultPercent averageResultPercent = new AverageResultPercent();
            averageResultPercent.setSubject(pair.getFirst());
            averageResultPercent.setResultPercent(pair.getSecond());
            diag.averageResultPercent.add(averageResultPercent);
        }

        this.setLearningYear(learningYear);
        this.setDiagnostics(diag);
    }
}
