package ru.portfolio.ax.rest.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class ProftechJobDTO {
    private String personId;
    private String recordId;
    private LocalDateTime creationDate;
    private LocalDateTime changeDateTime;
    private String eventId;
    private Integer employmentTypeId;
    private String employmentType;
    private Integer levelBusinessId;
    private String levelBusiness;
    private Integer salaryRangeId;
    private String salaryRange;
    private Boolean isDocForEmployment;
    private LocalDate docEmploymentDate;
    private Integer docTypeId;
    private String docType;
    private String position;
    private String mainFunctionality;
    private Boolean isByProfile;
    private String jobStudyPlace;
    private String organization;

}