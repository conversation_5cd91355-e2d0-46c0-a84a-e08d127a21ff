package ru.portfolio.ax.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckInResponseDTO implements AbstractDTO {

    private Institution institution;
    private String exceptionDescription;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Institution {
        @NotNull
        private Integer id;
        @NotNull
        private Integer type;
    }
}
