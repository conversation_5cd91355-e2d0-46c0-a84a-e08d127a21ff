package ru.portfolio.ax.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExcelSearchParamsDTO {
    private String schoolId;

    private String uuid;
    private String lastname;
    private String firstname;
    private String patronymic;
    private LocalDate birthdate;
    private String snils;
    private String phone;
    private String mail;
    private String parallel;
    private String grade;
    private String staffId;
    private LocalDate trainingOn;

    public void depersonalization(String uuid) {
        this.uuid = uuid;
        this.firstname = null;
        this.lastname = null;
        this.patronymic = null;
    }

    public void identification(String firstname, String lastname, String patronymic) {
        this.uuid = null;
        this.firstname = firstname;
        this.lastname = lastname;
        this.patronymic = patronymic;
    }
}
