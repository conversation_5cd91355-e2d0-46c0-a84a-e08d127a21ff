package ru.portfolio.ax.rest;

import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.*;
import ru.portfolio.ax.model.dto.CulturalInstitutionClickDTO;
import ru.portfolio.ax.model.dto.IndependentDiagnosticDTO;
import ru.portfolio.ax.model.ref.MetaskillRef;
import ru.portfolio.ax.rest.api.PortfolioApi;
import ru.portfolio.ax.rest.api.PositiveResponse;
import ru.portfolio.ax.rest.dto.*;
import ru.portfolio.ax.service.ClickHouseService;
import ru.portfolio.ax.service.DataService;
import ru.portfolio.ax.service.SearchService;
import ru.portfolio.ax.util.security.AuthComponent;
import ru.portfolio.ax.util.security.AuthComponentNew;
import ru.portfolio.ax.util.security.Secured;
import ru.portfolio.ax.util.security.SecuredNew;

import java.util.List;

@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/search")
public class SearchController {

    private final AuthComponent authComponent;
    private final SearchService searchService;
    private final AuthComponentNew authComponentNew;
    private final ClickHouseService clickHouseService;
    private final DataService dataService;
    @Value("${old.auth.enabled}")
    boolean oldAuthEnabled;

    //@Secured
    @ReadOnly
    @GetMapping("/{personId}/govexams")
    public PositiveResponse<List<StandardExamDTO>> searchGovexamsByString(@PathVariable String personId,
                                                                          @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchGovexamsByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchGovexamsByString(personId, searchString));
    }

    //@Secured
    @ReadOnly
    @GetMapping("/{personId}/additional-education")
    public PositiveResponse<CertificatesDTO> searchCertificateByString(@PathVariable String personId,
                                                                                         @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchCertificateByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchCertificateByString(personId, searchString));
    }

    //@Secured
    @ReadOnly
    @GetMapping("/{personId}/document")
    public PositiveResponse<List<Document>> searchDocumentByString(@PathVariable String personId,
                                                                   @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchDocumentByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchDocumentByString(personId, searchString));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/independent-diagnostic")
    public PositiveResponse<List<IndependentDiagnosticDTO>> searchIndependentDiagnosticByString(@PathVariable String personId,
                                                                                                @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchDiagnosticByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchIndependentDiagnosticByString(personId, searchString));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/diagnostic")
    public PositiveResponse<List<SelfDiagnosticWorkDTO>> searchDiagnosticByString(@PathVariable String personId,
                                                                                  @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchDiagnosticByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchDiagnosticByString(personId, searchString));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/culture")
    public PositiveResponse<List<CulturalInstitutionClickDTO>> searchCulturalInstitutionByString(@PathVariable String personId,
                                                                                                 @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchCulturalInstitutionByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchCulturalInstitutionByString(personId, searchString));
    }

    //@Secured
    @ReadOnly
    @GetMapping("/{personId}/events")
    public PositiveResponse<List<EventDTO>> searchEventsByString(@PathVariable String personId,
                                                                 @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchEventsByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchEventByString(personId, searchString));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/employments")
    public PositiveResponse<List<EmploymentDTO>> searchEmploymentsByString(@PathVariable String personId,
                                                                           @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchEmploymentsByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchEmploymentsByString(personId, searchString));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/projects")
    public PositiveResponse<List<ProjectDTO>> searchProjectsByString(@PathVariable String personId,
                                                                     @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchProjectsByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchProjectsByString(personId, searchString));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/rewards")
    public PositiveResponse<List<RewardDTO>> searchRewardsByString(@PathVariable String personId,
                                                                   @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchRewardsByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchRewardsByString(personId, searchString));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/sport-rewards")
    public PositiveResponse<List<SportRewardDTO>> searchSportRewardsByString(@PathVariable String personId,
                                                                             @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchSportRewardsByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchSportRewardsByString(personId, searchString));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/affilations")
    public PositiveResponse<List<Affilation>> searchAffilationByString(@PathVariable String personId,
                                                                       @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchAffilationByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchAffilationByString(searchString, personId));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/job")
    public PositiveResponse<ProfessionalEducationJobResponse> searchJobByString(@PathVariable String personId,
                                                                                @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchJobByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchJobByString(searchString, personId));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/gia-worldskills")
    public PositiveResponse<List<GIAWorldskills>> searchGiaWorldSkillsByString(@PathVariable String personId,
                                                                               @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchGiaWorldSkillsByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchGiaWorldSkillsByString(personId, searchString));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/interest")
    public PositiveResponse<List<InterestDTO>> searchInterestsByString(@PathVariable String personId,
                                                                       @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchInterestsByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchInterestsByString(personId, searchString));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/education")
    public PositiveResponse<ProfessionalEducationResponse> searchEducationByString(@PathVariable String personId,
                                                                                   @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchEducationByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchEducationByString(personId, searchString));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/check-in-history")
    public PositiveResponse<List<CheckInHistory>> searchCheckInHistoryByString(@PathVariable String personId,
                                                                               @RequestParam("searchString") String searchString) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchCheckInHistoryByString", personId);
        }
        return PortfolioApi.positiveResponse(
                searchService.searchCheckInHistoryByString(searchString, personId));
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/metaskills")
    public PositiveResponse<List<MetaskillRef>> searchMetaskillsByString(@PathVariable String personId,
                                                                         @RequestParam("searchString") String searchString) {
        authComponent.userAuth(Secured.Secure.standard(), "searchMetaskillsByString", personId);
        return PortfolioApi.positiveResponse(
                searchService.searchMetaskillsByString(searchString, personId));
    }
}
