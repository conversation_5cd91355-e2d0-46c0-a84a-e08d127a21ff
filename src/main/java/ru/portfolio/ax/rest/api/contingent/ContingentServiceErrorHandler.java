package ru.portfolio.ax.rest.api.contingent;

import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.DefaultResponseErrorHandler;
import ru.portfolio.ax.rest.exception.PortfolioException;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class ContingentServiceErrorHandler extends DefaultResponseErrorHandler {
    @Override
    public void handleError(ClientHttpResponse response) throws IOException {
        if (response.getStatusCode().is4xxClientError() || response.getStatusCode().is5xxServerError()) {
            int statusCode = response.getRawStatusCode();
            MediaType contentType = response.getHeaders().getContentType();
            if (Objects.nonNull(contentType) && errorContingentMap.containsKey(statusCode)) {
                throw errorContingentMap.get(statusCode);
            } else {
                throw new RuntimeException("error");
            }
        }
    }

    private final Map<Integer, PortfolioException> errorContingentMap = new HashMap<Integer, PortfolioException>() {
        {
            put(404, PortfolioException.get423());
            put(401, PortfolioException.get420());
            put(400, PortfolioException.get423());
            put(500, PortfolioException.get422());
        }
    };
}
