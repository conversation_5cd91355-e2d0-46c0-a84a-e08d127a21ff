package ru.portfolio.ax.rest.api.contingent.filter;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;

import java.util.Arrays;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ContingentFilterCollector {
    private List<ContingentFilter> and;
    private List<ContingentFilter> or;

    public static ContingentFilterCollector and(ContingentFilter... filters) {
        return ContingentFilterCollector.builder().and(Arrays.asList(filters)).build();
    }

    public static ContingentFilterCollector or(ContingentFilter... filters) {
        return ContingentFilterCollector.builder().or(Arrays.asList(filters)).build();
    }

    @SneakyThrows
    public String json(ObjectMapper mapper) {
        return mapper.writeValueAsString(this);
    }
}
