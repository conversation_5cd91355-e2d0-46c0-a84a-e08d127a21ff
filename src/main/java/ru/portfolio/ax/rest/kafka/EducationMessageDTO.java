package ru.portfolio.ax.rest.kafka;

import lombok.Data;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class EducationMessageDTO {
    private String personId;
    private String messageType;
    private ArrayList<Action> actions;

    @Data
    public static class Action {
        private String action;
        private String contestId;
        private String title;
        private Integer level;
        private List<Subjects> subjects;
        private LocalDate contestStartedAt;
        private LocalDate contestFinishedAt;
        private ZonedDateTime participatedFrom;
        private ZonedDateTime participatedTo;
        private Long score;
        private String rewardId;
        private String rewardPresentedAt;
        private Integer rewardType;
        private Boolean transmitStatus;
    }

    @Data
    public static class Subjects {
        private Long id;
        private String name;
    }
}


