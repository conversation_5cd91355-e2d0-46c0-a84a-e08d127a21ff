package ru.portfolio.ax.rest.kafka;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@ToString
@EqualsAndHashCode
public class NotificationMessage {

    @JsonProperty("communication_action_request_id")
    private String communicationActionRequestId;
    @JsonProperty("action_types")
    private String actionType;
    @JsonProperty("request_date_time")
    private LocalDateTime requestDateTime;

    private Who who;
    private What what;


    @Data
    @ToString
    @EqualsAndHashCode
    public static class Who {
        @JsonProperty("recipient_id")
        private String recipientId;
        @JsonProperty("recipient_type")
        private String recipientType;
    }

    @Data
    @ToString
    @EqualsAndHashCode
    public static class What {
        @JsonProperty("content_subject")
        private String contentSubject;
        @JsonProperty("content_data")
        private String contentData;
        @JsonProperty("context_variables")
        private ContextVariables contextVariables;
    }

    @Data
    @ToString
    @EqualsAndHashCode
    public static class ContextVariables {
        private Boolean html;
    }

    @Data
    @ToString
    @EqualsAndHashCode
    public static class Message {
        @JsonProperty("data")
        private MessageData data;
    }

    @Data
    @ToString
    @EqualsAndHashCode
    public static class MessageData {
        private String type;
        private String path;
    }
}
