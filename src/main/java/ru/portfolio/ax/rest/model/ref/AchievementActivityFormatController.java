package ru.portfolio.ax.rest.model.ref;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ru.portfolio.ax.model.ref.AchievementActivityFormatRef;
import ru.portfolio.ax.rest.ReadableRefController;
import ru.portfolio.ax.service.CrudService;

@RestController
@RequestMapping("/reference/achievement/activity/formats")
public class AchievementActivityFormatController extends ReadableRefController<Integer, AchievementActivityFormatRef> {
    public AchievementActivityFormatController(CrudService crudService) {
        super(AchievementActivityFormatRef.class, crudService);
    }
}
