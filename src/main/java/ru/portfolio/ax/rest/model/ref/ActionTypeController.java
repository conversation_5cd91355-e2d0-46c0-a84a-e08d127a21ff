package ru.portfolio.ax.rest.model.ref;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ru.portfolio.ax.model.ref.ActionTypeRef;
import ru.portfolio.ax.rest.ReadableController;
import ru.portfolio.ax.rest.ReadableRefController;
import ru.portfolio.ax.service.CrudService;

@RestController
@RequestMapping("/portfolio/action-types")
public class ActionTypeController extends ReadableRefController<Integer, ActionTypeRef> {
    public ActionTypeController(CrudService crudService) {
        super(ActionTypeRef.class, crudService);
    }
}
