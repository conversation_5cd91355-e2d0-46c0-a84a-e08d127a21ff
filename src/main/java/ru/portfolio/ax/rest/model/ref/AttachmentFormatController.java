package ru.portfolio.ax.rest.model.ref;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ru.portfolio.ax.model.ref.AttachmentFormatTypeRef;
import ru.portfolio.ax.rest.ReadableController;
import ru.portfolio.ax.rest.ReadableRefController;
import ru.portfolio.ax.service.CrudService;

@RestController
@RequestMapping("/reference/attachment/format/types")
public class AttachmentFormatController extends ReadableRefController<Integer, AttachmentFormatTypeRef> {
    public AttachmentFormatController(CrudService crudService) {
        super(AttachmentFormatTypeRef.class, crudService);
    }
}