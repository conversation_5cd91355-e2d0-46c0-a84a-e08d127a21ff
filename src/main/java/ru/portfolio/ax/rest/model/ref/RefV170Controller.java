package ru.portfolio.ax.rest.model.ref;

import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ru.portfolio.ax.model.ref.*;
import ru.portfolio.ax.model.ref.interest.*;
import ru.portfolio.ax.rest.ReadableRefController;
import ru.portfolio.ax.service.CrudService;

@Component
public class RefV170Controller {
    @RestController
    @RequestMapping("reference/section-types")
    public class SectionTypeRefController extends ReadableRefController<Integer, SectionTypeRef> {
        public SectionTypeRefController(CrudService crudService) {
            super(SectionTypeRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/tourism-kinds")
    public class TourismKindRefController extends ReadableRefController<Integer, TourismKindRef> {
        public TourismKindRefController(CrudService crudService) {
            super(TourismKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/sport-kinds")
    public class SportKindRefController extends ReadableRefController<Integer, SportKindRef> {
        public SportKindRefController(CrudService crudService) {
            super(SportKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/sport-rewards")
    public class SportRewardRefController extends ReadableRefController<Integer, SportRewardRef> {
        public SportRewardRefController(CrudService crudService) {
            super(SportRewardRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/sections")
    public class SectionRefController extends ReadableRefController<Integer, SectionRef> {
        public SectionRefController(CrudService crudService) {
            super(SectionRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/reward-kinds")
    public class RewardKindRefController extends ReadableRefController<Integer, RewardKindRef> {
        public RewardKindRefController(CrudService crudService) {
            super(RewardKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/event-kinds")
    public class EventKindRefController extends ReadableRefController<Integer, EventKindRef> {
        public EventKindRefController(CrudService crudService) {
            super(EventKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/creation-kinds")
    public class CreationKindRefController extends ReadableRefController<Integer, CreationKindRef> {
        public CreationKindRefController(CrudService crudService) {
            super(CreationKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/stop-words")
    public class StopWordRefController extends ReadableRefController<String, StopWordsRef> {
        public StopWordRefController(CrudService crudService) {
            super(StopWordsRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/subcategories")
    public class SubcategoryRefController extends ReadableRefController<Integer, SubcategoryRef> {
        public SubcategoryRefController(CrudService crudService) {
            super(SubcategoryRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/reward-statuses")
    public class StatusRewardRefController extends ReadableRefController<Integer, StatusRewardRef> {
        public StatusRewardRefController(CrudService crudService) {
            super(StatusRewardRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/cultural-kinds")
    public class CulturalKindRefController extends ReadableRefController<Integer, CulturalKindRef> {
        public CulturalKindRefController(CrudService crudService) {
            super(CulturalKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/project-formats")
    public class ProjectFormatRefController extends ReadableRefController<Integer, ProjectFormatRef> {
        public ProjectFormatRefController(CrudService crudService) {
            super(ProjectFormatRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/theatres")
    public class TheatreRefController extends ReadableRefController<Integer, TheatreRef> {
        public TheatreRefController(CrudService crudService) {
            super(TheatreRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/sport-clubs")
    public class SportClubRefController extends ReadableRefController<Integer, SportClubRef> {
        public SportClubRefController(CrudService crudService) {
            super(SportClubRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/museums")
    public class MuseumRefController extends ReadableRefController<Integer, MuseumRef> {
        public MuseumRefController(CrudService crudService) {
            super(MuseumRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/disciplines")
    public class DisciplineRefController extends ReadableRefController<Integer, DisciplineRef> {
        public DisciplineRefController(CrudService crudService) {
            super(DisciplineRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/cinemas")
    public class CinemaRefController extends ReadableRefController<Integer, CinemaRef> {
        public CinemaRefController(CrudService crudService) {
            super(CinemaRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/affilation-kinds")
    public class AffilationKindRefController extends ReadableRefController<Integer, AffilationKindRef> {
        public AffilationKindRefController(CrudService crudService) {
            super(AffilationKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/sport-ages")
    public class SportAgeRefController extends ReadableRefController<Integer, SportAgeRef> {
        public SportAgeRefController(CrudService crudService) {
            super(SportAgeRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/data-source")
    public class DataSourseRefController extends ReadableRefController<Integer, DataSourceRef> {
        public DataSourseRefController(CrudService crudService) {
            super(DataSourceRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/action")
    public class InterestActionKindRefController extends ReadableRefController<Integer, InterestActionKindRef> {
        public InterestActionKindRefController(CrudService crudService) {
            super(InterestActionKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/activity")
    public class InterestActivityKindRefController extends ReadableRefController<Integer, InterestActivityKindRef> {
        public InterestActivityKindRefController(CrudService crudService) {
            super(InterestActivityKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/animals")
    public class InterestAnimalPlantKindRefController extends ReadableRefController<Integer, InterestAnimalPlantKindRef> {
        public InterestAnimalPlantKindRefController(CrudService crudService) {
            super(InterestAnimalPlantKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/design")
    public class InterestArtDesignKindRefController extends ReadableRefController<Integer, InterestArtDesignKindRef> {
        public InterestArtDesignKindRefController(CrudService crudService) {
            super(InterestArtDesignKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/character")
    public class InterestCharacterKindRefController extends ReadableRefController<Integer, InterestCharacterKindRef> {
        public InterestCharacterKindRefController(CrudService crudService) {
            super(InterestCharacterKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/cinema")
    public class InterestCinemaRefController extends ReadableRefController<Integer, InterestCinemaKindRef> {
        public InterestCinemaRefController(CrudService crudService) {
            super(InterestCinemaKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/collection")
    public class InterestCollectionKindRefController extends ReadableRefController<Integer, InterestCollectionKindRef> {
        public InterestCollectionKindRefController(CrudService crudService) {
            super(InterestCollectionKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/cookery")
    public class InterestCookeryKindRefController extends ReadableRefController<Integer, InterestCookeryKindRef> {
        public InterestCookeryKindRefController(CrudService crudService) {
            super(InterestCookeryKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/cultural")
    public class InterestCulturalKindRefController extends ReadableRefController<Integer, InterestCulturalKindRef> {
        public InterestCulturalKindRefController(CrudService crudService) {
            super(InterestCulturalKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/dance")
    public class InterestDanceKindRefController extends ReadableRefController<Integer, InterestDanceKindRef> {
        public InterestDanceKindRefController(CrudService crudService) {
            super(InterestDanceKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/fashion")
    public class InterestFashionKindRefController extends ReadableRefController<Integer, InterestFashionKindRef> {
        public InterestFashionKindRefController(CrudService crudService) {
            super(InterestFashionKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/game")
    public class InterestGameKindRefController extends ReadableRefController<Integer, InterestGameKindRef> {
        public InterestGameKindRefController(CrudService crudService) {
            super(InterestGameKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/group")
    public class InterestGroupKindRefController extends ReadableRefController<Integer, InterestGroupKindRef> {
        public InterestGroupKindRefController(CrudService crudService) {
            super(InterestGroupKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/head")
    public class InterestHeadKindRefController extends ReadableRefController<Integer, InterestHeadKindRef> {
        public InterestHeadKindRefController(CrudService crudService) {
            super(InterestHeadKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/healthy")
    public class InterestHealthyLifestyleKindRefController extends ReadableRefController<Integer, InterestHealthyLifestyleKindRef> {
        public InterestHealthyLifestyleKindRefController(CrudService crudService) {
            super(InterestHealthyLifestyleKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/history")
    public class InterestHistoryKindRefController extends ReadableRefController<Integer, InterestHistoryKindRef> {
        public InterestHistoryKindRefController(CrudService crudService) {
            super(InterestHistoryKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/information")
    public class InterestInformationKindRefController extends ReadableRefController<Integer, InterestInformationKindRef> {
        public InterestInformationKindRefController(CrudService crudService) {
            super(InterestInformationKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/language")
    public class InterestLanguageKindRefController extends ReadableRefController<Integer, InterestLanguageKindRef> {
        public InterestLanguageKindRefController(CrudService crudService) {
            super(InterestLanguageKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/literature")
    public class InterestLiteratureKindRefController extends ReadableRefController<Integer, InterestLiteratureKindRef> {
        public InterestLiteratureKindRefController(CrudService crudService) {
            super(InterestLiteratureKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/music")
    public class InterestMusicKindRefController extends ReadableRefController<Integer, InterestMusicKindRef> {
        public InterestMusicKindRefController(CrudService crudService) {
            super(InterestMusicKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/photo")
    public class InterestPhotoKindRefController extends ReadableRefController<Integer, InterestPhotoKindRef> {
        public InterestPhotoKindRefController(CrudService crudService) {
            super(InterestPhotoKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/science")
    public class InterestScienceTechnologyKindRefController extends ReadableRefController<Integer, InterestScienceTechnologyKindRef> {
        public InterestScienceTechnologyKindRefController(CrudService crudService) {
            super(InterestScienceTechnologyKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/space")
    public class InterestSpaceKindRefController extends ReadableRefController<Integer, InterestSpaceKindRef> {
        public InterestSpaceKindRefController(CrudService crudService) {
            super(InterestSpaceKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/sport")
    public class InterestSportKindRefController extends ReadableRefController<Integer, InterestSportKindRef> {
        public InterestSportKindRefController(CrudService crudService) {
            super(InterestSportKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/theatrical")
    public class InterestTheatricalKindRefController extends ReadableRefController<Integer, InterestTheatricalKindRef> {
        public InterestTheatricalKindRefController(CrudService crudService) {
            super(InterestTheatricalKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/travel")
    public class InterestTourismTravelKindRefController extends ReadableRefController<Integer, InterestTourismTravelKindRef> {
        public InterestTourismTravelKindRefController(CrudService crudService) {
            super(InterestTourismTravelKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/transport")
    public class InterestTransportKindRefController extends ReadableRefController<Integer, InterestTransportKindRef> {
        public InterestTransportKindRefController(CrudService crudService) {
            super(InterestTransportKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/interest/social")
    public class InterestMediaSocialKindRefController extends ReadableRefController<Integer, InterestMediaSocialKindRef> {
        public InterestMediaSocialKindRefController(CrudService crudService) {
            super(InterestMediaSocialKindRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/training-stage")
    public class TrainingStageRefController extends ReadableRefController<Integer, TrainingStageRef> {
        public TrainingStageRefController(CrudService crudService) {
            super(TrainingStageRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/document")
    public class DocumentRefController extends ReadableRefController<Integer, DocumentRef> {
        public DocumentRefController(CrudService crudService) {
            super(DocumentRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/metaskill")
    public class MetaskillController extends ReadableRefController<Integer, MetaskillRef> {
        public MetaskillController(CrudService crudService) {
            super(MetaskillRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/profession-program")
    public class ProfessionProgramRefController extends ReadableRefController<Integer, ProfessionProgramRef> {
        public ProfessionProgramRefController(CrudService crudService) {
            super(ProfessionProgramRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/profession-rank")
    public class ProfessionRankRefController extends ReadableRefController<Integer, ProfessionRankRef> {
        public ProfessionRankRefController(CrudService crudService) {
            super(ProfessionRankRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/spo-gia-mark")
    public class SpoGiaMarkRefController extends ReadableRefController<Integer, SpoGiaMarkRef> {
        public SpoGiaMarkRefController(CrudService crudService) {
            super(SpoGiaMarkRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/spo-status")
    public class SpoStatusRefController extends ReadableRefController<Integer, SpoStatusRef> {
        public SpoStatusRefController(CrudService crudService) {
            super(SpoStatusRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/spo-organization")
    public class SpoOrganizationRefController extends ReadableRefController<Integer, SpoOrganizationRef> {
        public SpoOrganizationRefController(CrudService crudService) {
            super(SpoOrganizationRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/level-business")
    public class LevelBusinessRefController extends ReadableRefController<Integer, LevelBusinessRef> {
        public LevelBusinessRefController(CrudService crudService) {
            super(LevelBusinessRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/salary-range")
    public class SalaryRangeRefController extends ReadableRefController<Integer, SalaryRangeRef> {
        public SalaryRangeRefController(CrudService crudService) {
            super(SalaryRangeRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/employment-doc-type")
    public class EmploymentDocTypeRefController extends ReadableRefController<Integer, EmploymentDocTypeRef> {
        public EmploymentDocTypeRefController(CrudService crudService) {
            super(EmploymentDocTypeRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/study-program-base")
    public class StudyProgramBaseRefController extends ReadableRefController<Integer, StudyProgramBaseRef> {
        public StudyProgramBaseRefController(CrudService crudService) {
            super(StudyProgramBaseRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/basic-education")
    public class BasicEducationRefController extends ReadableRefController<Integer, BasicEducationRef> {
        public BasicEducationRefController(CrudService crudService) {
            super(BasicEducationRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/municipality")
    public class MunicipalityRefController extends ReadableRefController<Integer, MunicipalityRef> {
        public MunicipalityRefController(CrudService crudService) {
            super(MunicipalityRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/avatar-type")
    public class AvatarTypeRefController extends ReadableRefController<Integer, AvatarTypeRef> {
        public AvatarTypeRefController(CrudService crudService) {
            super(AvatarTypeRef.class, crudService);
        }
    }

    @RestController
    @RequestMapping("reference/event-organizators")
    public class OrganizatorRefController extends ReadableRefController<Integer, OrganizatorRef> {
        public OrganizatorRefController(CrudService crudService) {
            super(OrganizatorRef.class, crudService);
        }
    }
}