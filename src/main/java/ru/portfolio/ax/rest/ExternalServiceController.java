package ru.portfolio.ax.rest;

import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.rest.api.PortfolioApi;
import ru.portfolio.ax.rest.api.PositiveResponse;
import ru.portfolio.ax.service.ext.ProforientationService;
import ru.portfolio.ax.util.aspect.LoggedMethod;
import ru.portfolio.ax.util.security.AuthComponentNew;
import ru.portfolio.ax.util.security.SecuredNew;

@RestController
@RequiredArgsConstructor
@RequestMapping("/external")
public class ExternalServiceController {
    private final ProforientationService proforientationService;
    private final AuthComponentNew authComponent;

    @ReadOnly
    @LoggedMethod
    @GetMapping("proforientation/authentication")
    public PositiveResponse<String> getProfToken(@RequestHeader(required = false) String authorization) {
        authComponent.userAuth(SecuredNew.Secure.byCookieNotByPerson(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getProfToken", null);

        return PortfolioApi.positiveResponse(proforientationService.getAuthToken());
    }
}
