package ru.portfolio.ax.repository.ref;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import ru.portfolio.ax.model.common.AbstractRefEntity;
import ru.portfolio.ax.model.ref.SubjectsRef;
import ru.portfolio.ax.repository.AbstractRefRepository;

import java.util.Collection;
import java.util.List;

@NoRepositoryBean
public interface RefRepository<T extends AbstractRefEntity<Integer>> extends AbstractRefRepository<Integer, T> {
    default Class<Integer> getIdentityClazz() {
        return Integer.class;
    }

    List<T> findByCodeIn(Collection<Integer> code);

    T findByValueIgnoreCase(String value);

    List<T> findAllByValueIgnoreCase(String value);

    @Query(nativeQuery = true,
            value = "select * from portfolio.tourism_kind_ref where lower(value) in :values and parent_id = :parentId")
    List<T> findAllByValueInAndParentId(Collection<String> values, Integer parentId);

    List<T> findAllByValueIn(Collection<String> values);
}