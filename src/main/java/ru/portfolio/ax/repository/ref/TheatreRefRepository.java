package ru.portfolio.ax.repository.ref;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import ru.portfolio.ax.model.ref.TheatreRef;

import java.util.List;

public interface TheatreRefRepository extends RefRepository<TheatreRef> {
    @Override
    default Class<TheatreRef> getClazz() {
        return TheatreRef.class;
    }

    @Query(nativeQuery = true, value = "with point as (select st_point(:geocodeX, :geocodeY) as target) " +
            "select * from portfolio.theatre_ref, point where geocode_x + geocode_y > 0 " +
            " and st_contains(st_buffer(point.target, :radius), st_point(geocode_x, geocode_y)) " +
            " and is_archive = false;")
    List<TheatreRef> findAllInRadius(@Param("geocodeX") Double geocodeX, @Param("geocodeY") Double geocodeY, @Param("radius") Double radius);
}
