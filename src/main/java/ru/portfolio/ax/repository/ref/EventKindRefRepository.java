package ru.portfolio.ax.repository.ref;

import ru.portfolio.ax.model.ref.EventKindRef;

import java.util.List;

public interface EventKindRefRepository extends RefRepository<EventKindRef> {
    @Override
    default Class<EventKindRef> getClazz() {
        return EventKindRef.class;
    }

    EventKindRef findByValueAndCategoryCode(String value, Long categoryCode);

    List<EventKindRef> findAllByValue(String value);

    List<EventKindRef> findAllByCategoryCodeAndIsArchive(Long categoryCode, Boolean isArchive);
}
