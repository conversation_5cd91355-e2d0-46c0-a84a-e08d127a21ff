package ru.portfolio.ax.repository.ref.interest;

import ru.portfolio.ax.model.ref.interest.InterestFashionKindRef;
import ru.portfolio.ax.model.ref.interest.InterestGameKindRef;
import ru.portfolio.ax.repository.ref.RefRepository;

public interface InterestGameKindRefRepository extends RefRepository<InterestGameKindRef> {
    @Override
    default Class<InterestGameKindRef> getClazz() {
        return InterestGameKindRef.class;
    }
}