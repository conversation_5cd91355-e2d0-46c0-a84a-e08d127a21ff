package ru.portfolio.ax.repository.ref.interest;

import ru.portfolio.ax.model.ref.interest.InterestTheatricalKindRef;
import ru.portfolio.ax.repository.ref.RefRepository;

public interface InterestTheatricalKindRefRepository extends RefRepository<InterestTheatricalKindRef> {
    @Override
    default Class<InterestTheatricalKindRef> getClazz() {
        return InterestTheatricalKindRef.class;
    }
}