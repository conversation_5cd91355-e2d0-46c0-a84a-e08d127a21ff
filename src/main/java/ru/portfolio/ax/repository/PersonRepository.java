package ru.portfolio.ax.repository;

import org.springframework.transaction.annotation.Transactional;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.PersonId;

import java.util.List;

public interface PersonRepository extends EntityRepository<PersonId> {
    @Override
    default Class<PersonId> getClazz() {
        return PersonId.class;
    }

    @ReadOnly
    List<PersonId> findAllByMeshId(String meshId);

    @ReadOnly
    Boolean existsByMeshIdAndNsi1Id(String meshId, String nsi1Id);

    @ReadOnly
    Boolean existsByMeshIdAndNsi2Id(String meshId, String nsi2Id);

    @Transactional
    void deleteByMeshIdAndNsi1Id(String meshId, String nsi1Id);
    @Transactional
    void deleteByMeshIdAndNsi2Id(String meshId, String nsi2Id);
}
