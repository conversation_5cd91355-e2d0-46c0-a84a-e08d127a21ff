package ru.portfolio.ax.repository;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import ru.portfolio.ax.model.Applications;

import java.util.List;

public interface ApplicationsRepository  extends EntityRepository<Applications>{

    @Override
    default Class<Applications> getClazz() {
        return Applications.class;
    }

    @Query("SELECT app FROM Applications app " +
            "WHERE app.personId = :personId " +
            "AND app.isArchived = false " +
            "ORDER BY app.eventDate DESC")
    List<Applications> findByPersonId( @Param("personId") String personId);

    @Query("SELECT app FROM Applications app " +
            "WHERE app.application = 4 " +
            "AND app.isArchived = false " +
            "AND app.personId = :personId " +
            "ORDER BY app.eventDate DESC")
    List<Applications> findByPersonIdAndApp(@Param("personId") String personId);

    @Query("SELECT app FROM Applications app " +
            "WHERE app.application = :status " +
            "AND app.personId = :personId " +
            "AND app.isArchived = false ")
    List<Applications> findByPersonIdAndStatus(@Param("person") String personId,
                                               @Param("status") Integer statusPublication);
}