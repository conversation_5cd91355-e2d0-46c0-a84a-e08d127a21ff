package ru.portfolio.ax.repository;

import ru.portfolio.ax.model.FavoriteUniversity;

import java.util.List;
import java.util.Optional;

public interface FavoriteUniversityRepository extends EntityRepository<FavoriteUniversity> {
    @Override
    default Class<FavoriteUniversity> getClazz() {
        return FavoriteUniversity.class;
    }

    Optional<FavoriteUniversity> findFirstByPersonIdAndUniversityId(String personId, Integer universityId);

    List<FavoriteUniversity> findAllByPersonIdAndUniversityId(String personId, Integer universityId);
    List<FavoriteUniversity> findAllByPersonIdAndIsDelete(String personId, Boolean isDelete);

    Optional<FavoriteUniversity> findFirstByPersonIdAndUniversityIdAndIsDelete(String personId, Integer universityId, Boolean isDelete);
}