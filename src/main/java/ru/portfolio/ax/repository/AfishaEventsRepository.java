package ru.portfolio.ax.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import ru.portfolio.ax.model.AfishaEvents;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

public interface AfishaEventsRepository extends EntityRepository<AfishaEvents> {

    @Override
    default Class<AfishaEvents> getClazz() {
        return AfishaEvents.class;
    }

    @Query("SELECT ae FROM AfishaEvents ae " +
            "WHERE ae.eventId IN :ids " +
            "AND ae.endRegistrationAt > now()")
    List<AfishaEvents> findAllById(@Param("ids") Collection<String> ids);

    @Query("SELECT af FROM AfishaEvents af " +
            "WHERE af.eventType = :type " +
            "AND af.eventId NOT IN :ids " +
            "AND af.endRegistrationAt >= :date " +
            "AND af.collegeName IN :colleges ")
    List<AfishaEvents> findAllByNotIdAndTypeAndCollegeNameAndDate(@Param("type") Integer type, @Param("ids") List<String> ids, @Param("colleges") List<String> college, @Param("date") LocalDateTime date);

    @Query("SELECT af FROM AfishaEvents af " +
            "WHERE af.eventId IN :eventIds " +
            "AND af.endRegistrationAt > :now " )
    List<AfishaEvents> findAllByIdAndDate(@Param("eventIds") List<String> eventIds, @Param("now") LocalDateTime now);

    @Query("SELECT af FROM AfishaEvents af " +
            "WHERE af.eventId NOT IN :notIds " +
            "AND af.endRegistrationAt > :date " +
            "AND af.eventType = :type ")
    List<AfishaEvents> findAllByNotIdAndTypeAndDate(@Param("type") Integer type, @Param("notIds") List<String> notIds,@Param("date") LocalDateTime date);
}
