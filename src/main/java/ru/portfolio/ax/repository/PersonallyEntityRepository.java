package ru.portfolio.ax.repository;

import org.springframework.data.repository.NoRepositoryBean;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.Reward;
import ru.portfolio.ax.model.common.PersonallyEntity;

import java.util.List;
import java.util.Optional;

@NoRepositoryBean
public interface PersonallyEntityRepository<T extends PersonallyEntity> extends EntityRepository<T> {

    @ReadOnly
    @Deprecated // переиспользовать на exist + not in id
    List<T> findAllByHashCode(String hashCode);

    @ReadOnly
    Boolean existsByHashCode(String hashCode);

    @ReadOnly
    Boolean existsByHashCodeAndIsDeleteAndPersonId(String hashCode, Boolean isDeleted, String personId);


    @ReadOnly
    Optional<T> findByIdIgnoreWhereAnnotation(Long id);

    @ReadOnly
    Optional<T> findByIdAndPersonId(Long id, String personId);
}
