package ru.portfolio.ax.repository;

import org.springframework.data.jpa.repository.Query;
import ru.portfolio.ax.model.Document;

import java.util.List;
import java.util.Optional;

public interface DocumentRepository extends PersonallyEntityRepository<Document> {

    @Override
    default Class<Document> getClazz() {
        return Document.class;
    }

    List<Document> findAllByPersonId(String personId);

    List<Document> findAllByHashCode(String hash);

    @Query(nativeQuery = true, value = "select * " +
            "from portfolio.document " +
            "where (name % ?1 " +
            "   or doc_number % ?1 " +
            "   or reg_number % ?1 " +
            "   or issue_place % ?1 " +
            "   or profession % ?1 " +
            "   or organization_name % ?1 " +
            "   or education_program % ?1 " +
            "   or rank_code in (select code from portfolio.profession_rank_ref where value % ?1) " +
            "   or exam_mark_code in (select code from portfolio.spo_gia_mark_ref where value % ?1) " +
            "   or spo_organization_code in (select code from portfolio.spo_organization_ref where value % ?1) " +
            "   or profession_program_code in (select code from portfolio.profession_program_ref where value % ?1) " +
            "   or document_code in (select code from portfolio.document_ref where value % ?1)) " +
            "   and person_id = ?2 " +
            "   and is_delete = false ")
    List<Document> findByTrigram(String searchString, String personId);

    @Override
    @Query(nativeQuery = true, value = "select * from portfolio.document where id = ?")
    Optional<Document> findByIdIgnoreWhereAnnotation(Long id);
}