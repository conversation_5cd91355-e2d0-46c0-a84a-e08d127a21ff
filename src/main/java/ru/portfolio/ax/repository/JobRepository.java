package ru.portfolio.ax.repository;

import org.springframework.data.jpa.repository.Query;
import ru.portfolio.ax.model.Event;
import ru.portfolio.ax.model.Job;
import ru.portfolio.ax.model.LearnerOlympiadParallels;

import java.util.List;
import java.util.Optional;

public interface JobRepository extends PersonallyEntityRepository<Job> {

    @Override
    default Class<Job> getClazz() {
        return Job.class;
    }

    List<Job> findAllByHashCode(String hash);

    List<Job> findAllByPersonIdAndEventIdInAndIsDelete(String personId, List<String> eventId, Boolean isDelete);

    List<Job> findAllByPersonIdAndIsDelete(String personId, Boolean isDelete);

    @Query(nativeQuery = true, value = "select * " +
            "from portfolio.job " +
            "where (salary_range_code in (select code from portfolio.salary_range_ref where value % ?1) " +
            "   or business_level_code in (select code from portfolio.level_business_ref where value % ?1) " +
            "   or contract_type_code in (select code from portfolio.employment_doc_type_ref where value % ?1) " +
            "   or main_functionality % ?1  " +
            "   or position % ?1 " +
            "   or organization % ?1) " +
            "   and person_id = ?2 " +
            "   and is_delete = false ")
    List<Job> findByTrigram(String searchString, String personId);

    @Override
    @Query(nativeQuery = true, value = "select * from portfolio.job where id = ?")
    Optional<Job> findByIdIgnoreWhereAnnotation(Long id);
}
