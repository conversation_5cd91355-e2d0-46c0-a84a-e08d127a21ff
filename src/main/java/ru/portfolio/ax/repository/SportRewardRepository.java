package ru.portfolio.ax.repository;

import org.springframework.data.jpa.repository.Query;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.SportReward;

import java.util.List;
import java.util.Optional;

public interface SportRewardRepository extends PersonallyEntityRepository<SportReward> {

    @Override
    default Class<SportReward> getClazz() {
        return SportReward.class;
    }

    @ReadOnly
    @Query(value = "select * from portfolio.sport_reward where " +
            "person_id = ?1 " +
            "and type_code in ?2 " +
            "and is_delete = false ",
            nativeQuery = true)
    List<SportReward> findAllByPersonIdAndTypeCodeIn(String personId, List<Integer> codes);

    @Query(nativeQuery = true, value = "select * " +
            "from portfolio.sport_reward " +
            "where (name % ?1 " +
            "   or description % ?1 " +
            "   or category_code in (select code from portfolio.section_ref where value % ?1) " +
            "   or subcategory_code in (select code from portfolio.subcategory_ref where value % ?1) " +
            "   or type_code in (select code from portfolio.section_ref where value % ?1) " +
            "   or sport_kind_code in (select code from portfolio.sport_kind_ref where value % ?1) " +
            "   or tourism_kind_code in (select code from portfolio.tourism_kind_ref where value % ?1) " +
            "   or level_reward_code in (select code from portfolio.olympiad_type_ref where value % ?1) " +
            "   or sport_reward_code in (select code from portfolio.sport_reward_ref where value % ?1) " +
            "   or data_kind in (select code from portfolio.section_ref where value % ?1)) " +
            "   and person_id = ?2 " +
            "   and is_delete = false ")
    List<SportReward> findByTrigram(String searchString, String personId);

    @Override
    @Query(nativeQuery = true, value = "select * from portfolio.sport_reward where id = ?")
    Optional<SportReward> findByIdIgnoreWhereAnnotation(Long id);

    List<SportReward> findAllByEntityId(String id);

    @Query(nativeQuery = true, value = "select * from portfolio.sport_reward where person_id = ?1 and type_code = ?2")
    List<SportReward> findAllByPersonIdAndTypeCode(String personId, Integer typeCode);

    @Query(nativeQuery = true, value = "select * from portfolio.sport_reward " +
            "where person_id = ?1 and type_code = ?2 and is_delete = ?3")
    List<SportReward> findAllByPersonIdAndTypeCodeAndIsDelete(String personId, Integer typeCode, Boolean isDelete);
}
