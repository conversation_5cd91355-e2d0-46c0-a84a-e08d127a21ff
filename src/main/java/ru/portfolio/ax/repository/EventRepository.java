package ru.portfolio.ax.repository;

import org.springframework.data.jpa.repository.Query;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.Event;

import java.util.List;
import java.util.Optional;

public interface EventRepository extends PersonallyEntityRepository<Event> {

    @Override
    default Class<Event> getClazz() {
        return Event.class;
    }

    @ReadOnly
    @Query(value = "select * from portfolio.event where " +
            "person_id = ?1 " +
            "and type_code in ?2 " +
            "and is_delete = false",
            nativeQuery = true)
    List<Event> findAllByPersonIdAndTypeCodeIn(String personId, List<Integer> codes);

    List<Event> findAllByPersonIdAndContestIdAndSourceCode(String personId, String contestId, Integer sourceCode);

    List<Event> findAllByPersonIdAndSourceCode(String personId, Integer sourceCode);

    List<Event> findAllByPersonIdAndSourceCodeAndIsDelete(String personId, Integer sourceCode, Boolean isDelete);

    @Query(nativeQuery = true, value = "select * " +
            "from portfolio.event " +
            "where (name % ?1 " +
            "   or description % ?1 " +
            "   or result % ?1 " +
            "   or organizators % ?1 " +
            "   or category_code in (select code from portfolio.section_ref where value % ?1) " +
            "   or subcategory_code in (select code from portfolio.subcategory_ref where value % ?1) " +
            "   or type_code in (select code from portfolio.section_ref where value % ?1) " +
            "   or array(select code from portfolio.discipline_ref where value % ?1) && (CAST((string_to_array(event.discipline_code, ',')) as smallint[])) " +
            "   or sport_kind_code in (select code from portfolio.sport_kind_ref where value % ?1) " +
            "   or tourism_kind_code in (select code from portfolio.tourism_kind_ref where value % ?1) " +
            "   or creation_kind_code in (select code from portfolio.creation_kind_ref where value % ?1) " +
            "   or data_kind in (select code from portfolio.section_ref where value % ?1) " +
            "   or format_code in (select code from portfolio.olympiad_format_ref where value % ?1) " +
            "   or level_event_code in (select code from portfolio.olympiad_type_ref where value % ?1) " +
            "   or level_olympiad_code in (select code from portfolio.olympiad_level_ref where value % ?1) " +
            "   or array(select code from portfolio.subjects_ref where value % ?1) && (CAST((string_to_array(event.subject_code, ',')) as smallint[])) " +
            "   or event_kind_code in (select code from portfolio.event_kind_ref as ref where value % ?1 or ref.organizators % ?1)) " +
            "   and person_id = ?2 " +
            "   and is_delete = false ")
    List<Event> findByTrigram(String searchString, String personId);

    @Override
    @Query(nativeQuery = true, value = "select * from portfolio.event where id = ?")
    Optional<Event> findByIdIgnoreWhereAnnotation(Long id);
}
