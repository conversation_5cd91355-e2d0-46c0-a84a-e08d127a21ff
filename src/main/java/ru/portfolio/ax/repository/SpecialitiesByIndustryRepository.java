package ru.portfolio.ax.repository;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import ru.portfolio.ax.model.SpecialitiesByIndustry;

import java.util.List;

public interface SpecialitiesByIndustryRepository extends EntityRepository<SpecialitiesByIndustry> {

    @Override
    default Class<SpecialitiesByIndustry> getClazz() {
        return SpecialitiesByIndustry.class;
    }

    @Query("SELECT sbi FROM SpecialitiesByIndustry sbi " +
            "JOIN AfishaEvents ae ON ae.eventId = sbi.eventId " +
            "WHERE ae.eventType = :type " +
            "ORDER BY ae.endRegistrationAt DESC")
    List<SpecialitiesByIndustry> findAllByEventTypeCode(@Param("type") Integer type, Pageable pageable);

    @Query("SELECT sp FROM SpecialitiesByIndustry sp " +
            "JOIN AfishaEvents af ON af.eventId = sp.eventId " +
            "WHERE sp.eventId IN :eventIds " +
            "AND sp.codeIndustry NOT IN :visited " +
            "AND sp.codeSpeciality IN :specsCodes " +
            "AND af.eventType = :type " +
            "ORDER BY af.endRegistrationAt DESC ")
    List<SpecialitiesByIndustry> findAllByFullParams(@Param("type") Integer type,
                                                     @Param("eventIds") List<String> eventIds,
                                                     @Param("specsCodes") List<String> specsCodes,
                                                     @Param("visited") List<String> visited,
                                                     Pageable pageable);

    @Query("SELECT sp FROM SpecialitiesByIndustry sp " +
            "JOIN AfishaEvents af ON af.eventId = sp.eventId " +
            "WHERE sp.eventId IN :eventIds " +
            "AND sp.codeIndustry NOT IN :visited " +
            "AND sp.codeSpeciality IN :specsCodes " +
            "AND af.eventType = :type " +
            "ORDER BY af.endRegistrationAt DESC")
    List<SpecialitiesByIndustry> findAllByMiddleParams(@Param("type") Integer type,
                                                       @Param("eventIds") List<String> eventIds,
                                                       @Param("specsCodes") List<String> specsCodes,
                                                       @Param("visited") List<String> visited,
                                                       Pageable pageable);

    @Query("SELECT sp FROM SpecialitiesByIndustry sp " +
            "JOIN AfishaEvents af ON af.eventId = sp.eventId " +
            "WHERE sp.eventId IN :eventIds " +
            "AND sp.codeIndustry IN :industryNames " +
            "AND sp.codeIndustry NOT IN :notIndustryNames " +
            "AND af.eventType = :type " +
            "ORDER BY af.endRegistrationAt DESC")
    List<SpecialitiesByIndustry> findAllByShortenParams(@Param("type") Integer type,
                                                        @Param("eventIds") List<String> eventIds,
                                                        @Param("industryNames") List<String> industryNames,
                                                        @Param("notIndustryNames") List<String> notIndustryIds,
                                                        Pageable pageable);

    @Query("SELECT sp FROM SpecialitiesByIndustry sp " +
            "JOIN AfishaEvents af ON af.eventId = sp.eventId " +
            "WHERE af.eventType = 42 " +
            "AND sp.eventId NOT IN :notEventIds " +
            "AND sp.codeIndustry IN :codes " +
            "ORDER BY af.endRegistrationAt DESC")
    List<SpecialitiesByIndustry> findAllByNotIdsAndByCodeIndustry(@Param("notEventIds") List<String> notEventIds,
                                                                  @Param("codes") List<String> codes,
                                                                  Pageable pageable);

    @Query("SELECT sp FROM SpecialitiesByIndustry sp " +
            "JOIN AfishaEvents af ON af.eventId = sp.eventId " +
            "WHERE sp.eventId NOT IN :eventsIds " +
            "AND sp.nameIndustry IN :industryNames " +
            "AND af.eventType = :type " +
            "ORDER BY af.endRegistrationAt DESC")
    List<SpecialitiesByIndustry> findAllByShortParams(@Param("type") Integer type,
                                                      @Param("eventsIds") List<String> eventIds,
                                                      @Param("industryNames") List<String> industryNames,
                                                      Pageable pageable);

    @Query("SELECT sp FROM SpecialitiesByIndustry sp " +
            "JOIN AfishaEvents af ON af.eventId = sp.eventId " +
            "WHERE sp.eventId NOT IN :notEventsIds " +
            "AND sp.nameIndustry IN :industryNames " +
            "AND af.eventType = :type " +
            "ORDER BY af.endRegistrationAt DESC")
    List<SpecialitiesByIndustry> findAllByTinyParams(@Param("type") Integer type,
                                                     @Param("notEventsIds") List<String> notEventIds,
                                                     @Param("industryNames") List<String> industryNames,
                                                     Pageable pageable);

    @Query("SELECT sp FROM SpecialitiesByIndustry sp " +
            "WHERE sp.eventId IN :ids")
    List<SpecialitiesByIndustry> findAllByEvents(@Param("ids") List<String> ids);

    @Query("SELECT sp FROM SpecialitiesByIndustry sp " +
            "JOIN AfishaEvents af ON af.eventId = sp.eventId " +
            "WHERE sp.eventId IN :eventsIds " +
            "AND sp.codeSpeciality IN :specsCodes " +
            "AND af.eventType = :type " +
            "ORDER BY af.endRegistrationAt DESC ")
    List<SpecialitiesByIndustry> findAllByParamsForOpenDoors(@Param("type") Integer type,
                                                             @Param("eventsIds") List<String> eventIds,
                                                             @Param("specsCodes") List<String> specCodes,
                                                             Pageable pageable);
}
