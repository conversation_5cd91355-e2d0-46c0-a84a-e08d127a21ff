package ru.portfolio.ax.service.ext;

import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import ru.portfolio.ax.configuration.DOPProperty;
import ru.portfolio.ax.rest.dto.dop.DPOProgram;
import ru.portfolio.ax.util.RestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
public class DOPService {

    private final String token;
    private final String dopUrl;
    private final RestTemplate dop;

    public DOPService(DOPProperty dopProperty,
                      RestTemplate dopRestTemplate) {
        this.token = dopProperty.getToken();
        this.dopUrl = dopProperty.getUrl();
        this.dop = dopRestTemplate;
    }

    public List<DPOProgram> getCurrentUserRoles(Integer oktmo, Integer age) {
        String url = dopUrl + "/recommendations" + "?municipality_oktmo=" + oktmo + "&age=" + age;
        ResponseEntity<DPOProgram[]> httpEntity = dop.exchange(UriComponentsBuilder.fromHttpUrl(url).build().toUri(),
                HttpMethod.GET, RestUtils.createDopHeaderEntity(null, token), DPOProgram[].class);
        return Arrays.asList(Objects.requireNonNull(httpEntity.getBody()));
    }

}
