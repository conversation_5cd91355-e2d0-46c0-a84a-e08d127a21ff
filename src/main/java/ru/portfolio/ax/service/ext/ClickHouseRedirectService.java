package ru.portfolio.ax.service.ext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.NullNode;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.hibernate.validator.internal.util.ReflectionHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import ru.portfolio.ax.configuration.ClickHouseProperty;
import ru.portfolio.ax.util.RestUtils;
import ru.portfolio.ax.util.Utils;
import ru.portfolio.ax.util.URLParser;

import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static ru.portfolio.ax.PortfolioApplication.getRestTemplateWithoutClickhouseInterceptor;

@Aspect
@Component
public class ClickHouseRedirectService {
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final ClickHouseProperty clickhouseProperty;
    @Value("${click-house.connector-host}")
    private String host;
    @Value("${click-house.connector-enable}")
    private Boolean enabled;
    @Value("${click-house.connector-fail-on-error}")
    private Boolean failOnError;

    public ClickHouseRedirectService(ClickHouseProperty property,
                                     URLParser urlParser, ObjectMapper objectMapper, @Value("${logging.interceptor.enabled}") Boolean loggingInterceptor) {
        this.clickhouseProperty = property;
        this.objectMapper = objectMapper;
        this.restTemplate = getRestTemplateWithoutClickhouseInterceptor(
                property.getConnectionTimeout(), property.getDataTransferTimeout(), urlParser, loggingInterceptor);
    }

    public Boolean swap() {
        return enabled = !enabled;
    }

    @Pointcut("execution(* ru.portfolio.ax.repository.Impl.ClickHouseRepositoryImpl.*(..))")
    public void handleClickHouseRequest() {
    }

    @Around("handleClickHouseRequest()")
    public Object handleClickHouseRequest(ProceedingJoinPoint pjp) throws Throwable {
        if (BooleanUtils.isFalse(enabled)) {
//            return Profiler.doSmth(() -> pjp.proceed(pjp.getArgs()));
            return pjp.proceed();
        }
        Signature signature = pjp.getSignature();
        String name = signature.getName();
        MethodSignature sig = (MethodSignature) signature;
        Map<String, Object> map = IntStream.range(0, sig.getParameterNames().length).boxed().collect(Collectors
                .toMap(i -> sig.getParameterNames()[i], i -> convert(pjp.getArgs()[i]), (a, b) -> a, LinkedHashMap::new));
        map.values().removeIf(ObjectUtils::isEmpty);
        Class<?> returnType = sig.getReturnType();
        Type type = sig.getMethod().getGenericReturnType();
//        return Profiler.doSmth(() -> call(host, name, map, returnType, type));
        return call(host, name, map, returnType, type);
    }

    public Object call(String host, String route, Map<String, Object> args, Class<?> clazz, Type type) {
        String uriParams = args.entrySet().stream()
                // .filter(e -> ObjectUtils.isNotEmpty(e.getValue()))
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&"));
        String url = host + route + "?" + uriParams;//        Utils.safetyGet()
        ResponseEntity<?> forEntity = Utils.safetyGet(() -> restTemplate.exchange(
                url, HttpMethod.GET, RestUtils.createHeaderEntity(null, clickhouseProperty.getApiKey()), clazz, args),
                e -> !failOnError);
//        ResponseEntity<?> forEntity = Utils.safetyGet(() -> restTemplate.getForEntity(url, clazz, args), e -> !failOnError);
        Object result;
        if (!failOnError && Objects.isNull(forEntity)) {
            if (ReflectionHelper.isIterable(clazz)) {
                result = Collections.emptyList();
            } else {
                result = NullNode.getInstance();
            }
        } else {
            result = forEntity.getBody();//fixme npe or safetyGet?
        }
        return objectMapper.convertValue(result,
                objectMapper.getTypeFactory().constructType(type));
    }

    private String convert(Object value) {
        if (ObjectUtils.isEmpty(value)) return StringUtils.EMPTY;
        if (ReflectionHelper.isIterable(value.getClass())) {
            return StringUtils.join((Collection<?>) value, ',');
        }
        return Objects.toString(value);
    }
}
