package ru.portfolio.ax.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import ru.portfolio.ax.model.*;
import ru.portfolio.ax.model.dto.AchievementClickDTO;
import ru.portfolio.ax.model.dto.CulturalInstitutionClickDTO;
import ru.portfolio.ax.model.dto.IndependentDiagnosticDTO;
import ru.portfolio.ax.model.dto.SelfDiagnosticClickDTO;
import ru.portfolio.ax.model.enums.EventType;
import ru.portfolio.ax.model.ref.MetaskillRef;
import ru.portfolio.ax.repository.*;
import ru.portfolio.ax.repository.ref.MetaskillRefRepository;
import ru.portfolio.ax.rest.dto.*;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.*;
import static java.util.Objects.nonNull;
import static ru.portfolio.ax.model.enums.PersonallyEntityEnum.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class SearchService {
    private final CrudService crudService;
    private final ClickHouseRepository clickHouseRepository;
    private final EmplRepository emplRepository;
    private final EventRepository eventRepository;
    private final RewardRepository rewardRepository;
    private final ProjectRepository projectRepository;
    private final SportRewardRepository sportRewardRepository;
    private final DocumentRepository documentRepository;
    private final JobRepository jobRepository;
    private final AffRepository affRepository;
    private final GiaExamRepository giaExamRepository;
    private final CheckInHistoryRepository checkInHistoryRepository;
    private final InterestRepository interestRepository;
    private final GIAWorldskillsRepository giaWorldskillsRepository;
    private final IndependentDiagnosticVisibleRepository independentDiagnosticVisibleRepository;
    private final MetaskillRefRepository metaskillRefRepository;
    private final ClickHouseService clickHouseService;
    private final DataService dataService;

    public List<InterestDTO> searchInterestsByString(String personId, String searchString) {
        List<Interest> interests = interestRepository.findByTrigram(searchString, personId);
        List<InterestDTO> interestDTO = dataService.mapToInterestDto(interests);
        dataService.fillInterestRefs(interestDTO);
        return interestDTO;
    }

    public List<GIAWorldskills> searchGiaWorldSkillsByString(String personId, String searchString) {
        List<GIAWorldskills> byTrigram = giaWorldskillsRepository.findByTrigram(searchString, personId);
        clickHouseService.fillLinkedObject(byTrigram);
        byTrigram.forEach(x -> x.reachTransient(crudService));
        return byTrigram;
    }

    public ProfessionalEducationJobResponse searchJobByString(String searchString, String personId) {
        List<Job> jobsFromPostgres = jobRepository.findByTrigram(searchString, personId);
        List<ProftechJobDTO> clickJobs = clickHouseRepository.searchProfessionalEducationJobBySearchString(personId, searchString);
        List<String> recordIds = jobsFromPostgres.stream()
                .map(Job::getRecordId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        clickJobs.addAll(clickHouseRepository.findProfessionalEducationJobByIds(personId, recordIds));
        List<String> eventIds = clickJobs.stream()
                .map(ProftechJobDTO::getEventId)
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        List<ProfessionalEducationJobResponse.Job> jobs = new ArrayList<>(clickHouseService.mapToJob(clickJobs, personId));
        dataService.fillEducationJobListFromClick(jobs, clickJobs, personId, eventIds);
        dataService.fillEducationJobListFromPostgres(jobs, jobsFromPostgres);
        ProfessionalEducationJobResponse response = new ProfessionalEducationJobResponse();
        response.setJob(jobs);
        return response;
    }

    public ProfessionalEducationResponse searchEducationByString(String personId, String searchString) {
        List<ProftechDTO> proftechData = clickHouseRepository.searchProfessionalEducationBySearchString(personId, searchString);
        ProfessionalEducationResponse response = new ProfessionalEducationResponse();
        List<ProfessionalEducationResponse.ProfEducation> profEducations = new ArrayList<>();
        List<ProfessionalEducationResponse.Practice> practices = new ArrayList<>();
        dataService.fillProfessionalEducation(proftechData, profEducations, practices);
        response.setProfEducations(profEducations);
        response.setPractices(practices);
        return response;
    }

    public List<Document> searchDocumentByString(String personId, String searchString) {
        List<Document> documents = documentRepository.findByTrigram(searchString, personId);
        documents.forEach(x -> x.reachTransient(crudService));
        return documents;
    }

    public CertificatesDTO searchCertificateByString(String personId, String searchString) {
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        List<CertificatesDTO.Certificate> certificates = clickHouseRepository.searchCertificatesBySearchString(personIds, searchString);
        CertificatesDTO certificatesDTO = new CertificatesDTO();
        certificatesDTO.setPersonId(personId);
        if (certificates.isEmpty()) {
            return certificatesDTO;
        } else {
            certificates.sort(comparing(CertificatesDTO.Certificate::getEventDate)
                    .thenComparing(CertificatesDTO.Certificate::getEventTime).reversed());
            certificatesDTO.getCertificate().addAll(certificates);
            return certificatesDTO;
        }
    }

    public List<StandardExamDTO> searchGovexamsByString(String personId, String searchString) {
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        List<GiaExam> exams = giaExamRepository.findByTrigram(searchString, personIds);
        return exams.stream().map(StandardExamDTO::build).collect(Collectors.toList());
    }

    public List<CulturalInstitutionClickDTO> searchCulturalInstitutionByString(String personId, String searchString) {
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        return clickHouseRepository.searchCulturalInstitutions(personIds, searchString);
    }

    public List<IndependentDiagnosticDTO> searchIndependentDiagnosticByString(String personId, String searchString) {
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        List<IndependentDiagnosticDTO> result = clickHouseRepository.searchIndependentDiagnosticByString(personIds, searchString);
        if (result.isEmpty()) return result;
        List<IndependentDiagnosticVisible> visibleList =
                independentDiagnosticVisibleRepository.findByPersonIdAndRecordIdIn(personId, result.stream()
                        .map(IndependentDiagnosticDTO::getRecordId).collect(Collectors.toList()));
        for (IndependentDiagnosticDTO dto : result) {
            IndependentDiagnosticVisible visible = visibleList.stream()
                    .filter(x -> x.getRecordId().equals(dto.getRecordId()))
                    .findFirst().orElse(null);
            if (nonNull(visible)) {
                dto.setIsVisible(visible.getIsVisible());
            }
        }
        return result;
    }

    public List<SelfDiagnosticWorkDTO> searchDiagnosticByString(String personId, String searchString) {
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        List<SelfDiagnosticClickDTO> diagnostics = clickHouseRepository.searchDiagnostic(personIds, searchString)
                .stream().filter(x -> nonNull(x.getSubjectName())).collect(Collectors.toList());

        if (diagnostics.isEmpty()) {
            return new ArrayList<>();
        }

        Set<String> subjects = diagnostics.stream().map(SelfDiagnosticClickDTO::getSubjectName).collect(Collectors.toSet());
        List<List<SelfDiagnosticClickDTO>> allBySubjects = new ArrayList<>();

        subjects.forEach(x -> {
            ArrayList<SelfDiagnosticClickDTO> diagnostic =
                    diagnostics.stream().filter(y -> y.getSubjectName().equals(x)).collect(Collectors.toCollection(ArrayList::new));
            allBySubjects.add(diagnostic);
        });

        List<SelfDiagnosticWorkDTO> response = new ArrayList<>();
        allBySubjects.stream().filter(x -> !x.isEmpty()).collect(Collectors.toList()).forEach(diagnostic -> {
            SelfDiagnosticWorkDTO dto = new SelfDiagnosticWorkDTO();

            dto.setTotal(diagnostic.size());
            //5.2
            clickHouseService.makeGroupMarks(diagnostic, dto);
            //5.3
            clickHouseService.makeGroupDuration(diagnostic, dto);
            //5.4
            clickHouseService.calculateCountUniqSubjects(diagnostic, dto);
            clickHouseService.calculateCountUniqTypes(diagnostic, dto);
            //5.5
            if (!diagnostic.isEmpty())
                dto.setEventDate(diagnostic.get(0).getEventDate());

            dto.setAverageResult(clickHouseService.calculateAvgPercentExecution(diagnostic));
            dto.setDurationResult(clickHouseService.calculateAvgDurationExecution(diagnostic));
            dto.setBestResult(clickHouseRepository.findBestResult(
                    personIds, EventType.SELF_DIAGNOSTIC.getCode(), 1, null, null));
            response.add(dto);
        });

        return response;
    }

    public List<EventDTO> searchEventByString(String personId, String searchString) {
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        List<AchievementClickDTO> achievements = clickHouseRepository.searchAchievements(personIds,
                EVENT.getAchievementType(), searchString, null);
        List<EventDTO> eventDTOList = clickHouseService.mapToEvents(achievements, personId);
        //List<OlympiadDTO.OlympiadClickDTO> olympiadClickDTOS = clickHouseRepository.searchOlympiads(personIds, searchString);
        //eventDTOList.addAll(mapOlympiadsFromClickHouse(olympiadClickDTOS, personId));
        List<Event> byTrigram = eventRepository.findByTrigram(searchString, personId);
        byTrigram.forEach(x -> x.reachTransient(crudService));
        eventDTOList.addAll(clickHouseService.mapToEventDto(byTrigram));
        clickHouseService.fillLinkedObject(eventDTOList);
        eventDTOList.sort(nullsLast(comparing(EventDTO::getStartDate, nullsLast(reverseOrder()))));
        return eventDTOList;
    }

    public List<EmploymentDTO> searchEmploymentsByString(String personId, String searchString) {
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        List<AchievementClickDTO> achievements = clickHouseRepository.searchAchievements(personIds,
                EMPLOYMENT.getAchievementType(), searchString, null);
        List<EmploymentDTO> employmentDTOList = clickHouseService.mapToEmployment(achievements, personId);
        List<Employment> byTrigram = emplRepository.findByTrigram(searchString, personId);
        byTrigram.forEach(x -> x.reachTransient(crudService));
        employmentDTOList.addAll(clickHouseService.mapToEmploymentDto(byTrigram));
        clickHouseService.fillLinkedObject(employmentDTOList);
        employmentDTOList.sort(nullsLast(comparing(EmploymentDTO::getStartDate, nullsLast(reverseOrder()))));
        return employmentDTOList;
    }

    public List<ProjectDTO> searchProjectsByString(String personId, String searchString) {
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        List<AchievementClickDTO> achievements = clickHouseRepository.searchAchievements(personIds,
                PROJECT.getAchievementType(), searchString, null);
        List<ProjectDTO> projectDTOList = clickHouseService.mapToProject(achievements, personId);
        List<Project> byTrigram = projectRepository.findByTrigram(searchString, personId);
        byTrigram.forEach(x -> x.reachTransient(crudService));
        projectDTOList.addAll(clickHouseService.mapToProjectDto(byTrigram));
        clickHouseService.fillLinkedObject(projectDTOList);
        projectDTOList.sort(nullsLast(comparing(ProjectDTO::getStartDate, nullsLast(reverseOrder()))));
        return projectDTOList;
    }

    public List<RewardDTO> searchRewardsByString(String personId, String searchString) {
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        List<AchievementClickDTO> achievements = clickHouseRepository.searchAchievements(personIds,
                REWARD.getAchievementType(), searchString, Arrays.asList(0, 2, 3, 4, 5, 6, 7));
        List<RewardDTO> rewardDTOList = clickHouseService.mapToReward(achievements, personId);
        List<Reward> byTrigram = rewardRepository.findByTrigram(searchString, personId);
        byTrigram.forEach(x -> x.reachTransient(crudService));
        rewardDTOList.addAll(clickHouseService.mapToRewardDto(byTrigram));
        clickHouseService.fillLinkedObject(rewardDTOList);
        rewardDTOList.sort(nullsLast(comparing(RewardDTO::getDate, nullsLast(reverseOrder()))));
        return rewardDTOList;
    }

    public List<SportRewardDTO> searchSportRewardsByString(String personId, String searchString) {
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        List<AchievementClickDTO> achievements = clickHouseRepository.searchAchievements(personIds,
                SPORT_REWARD.getAchievementType(), searchString, Collections.singletonList(1));
        List<SportRewardDTO> sportRewardDTOList = clickHouseService.mapToSportReward(achievements, personId);
        List<SportReward> byTrigram = sportRewardRepository.findByTrigram(searchString, personId);
        byTrigram.forEach(x -> x.reachTransient(crudService));
        sportRewardDTOList.addAll(clickHouseService.mapToSportRewardDto(byTrigram));
        clickHouseService.fillLinkedObject(sportRewardDTOList);
        sportRewardDTOList.sort(nullsLast(comparing(SportRewardDTO::getDate, nullsLast(reverseOrder()))));
        return sportRewardDTOList;
    }

    public List<Affilation> searchAffilationByString(String searchString, String personId) {
        List<Affilation> byTrigram = affRepository.findByTrigram(searchString, personId);
        clickHouseService.fillLinkedObject(byTrigram);
        byTrigram.forEach(x -> x.reachTransient(crudService));
        return byTrigram;
    }

    public List<CheckInHistory> searchCheckInHistoryByString(String searchString, String personId) {
        List<String> personIds = new ArrayList<>(clickHouseService.getPersonIds(personId));
        List<CheckInHistory> result = checkInHistoryRepository.findByTrigram(searchString, personIds);
        result.forEach(x -> x.reachTransient(crudService));
        return result;
    }

    public List<MetaskillRef> searchMetaskillsByString(String searchString, String personId) {
        List<String> personIds = new ArrayList<>(clickHouseService.getPersonIds(personId));
        List<MetaskillRef> result = metaskillRefRepository.findByTrigram(searchString, personIds);
        return result;
    }
}
