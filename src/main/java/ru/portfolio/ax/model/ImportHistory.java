package ru.portfolio.ax.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import ru.portfolio.ax.model.common.AbstractEntity;

import javax.persistence.Entity;
import java.time.LocalDateTime;


@Data
@Entity
@EqualsAndHashCode(callSuper = true)
public class ImportHistory extends AbstractEntity<Long> {
    private LocalDateTime creationDate;
    private Integer categoryCode;
    private Integer dataKind;
    private Integer typeCode;
    private String fileName;
    private Integer correctCount;
    private Integer incorrectCount;
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String report;
    private String creatorId;
}
