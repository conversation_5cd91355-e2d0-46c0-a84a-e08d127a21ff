package ru.portfolio.ax.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import ru.portfolio.ax.model.common.AbstractEntity;

import javax.persistence.Entity;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class LearnerClassPeriod extends AbstractEntity<Long> {
    private UUID learnerId;
    private UUID classId;
    private Integer classLevel;
    private LocalDateTime classPeriodStartDate;
    private LocalDateTime classPeriodEndDate;
}
