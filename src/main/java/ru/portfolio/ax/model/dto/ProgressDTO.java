package ru.portfolio.ax.model.dto;

import lombok.Data;
import ru.portfolio.ax.rest.dto.enums.MarkEventType;

import java.time.LocalDate;

@Data
public class ProgressDTO {
    private Integer subjectId;
    private String subjectName;
    private Integer markedSubjectId;
    private String markedSubjectName;
    private Integer absenceSubjectId;
    private String absenceSubjectName;
    private Integer value5;
    private Integer value100;
    private Integer weight;
    private MarkEventType markEventType;
    private Integer schoolId;
    private LocalDate eventDate;
    private String profile;
}
