package ru.portfolio.ax.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import ru.portfolio.ax.model.common.AuditableEntity;
import ru.portfolio.ax.model.ref.SectionTypeRef;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import java.util.UUID;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class NotificationSetting extends AuditableEntity<Long> {
    private UUID personId;
    @Column(name = "is_delete")
    private boolean deleted = false;
    @ManyToOne
    private User user;
    @ManyToOne
    private PersonNotificationSetting personNotificationSetting;
    @ManyToOne
    private SectionTypeRef sectionType;
}
