package ru.portfolio.ax.model.common;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import ru.portfolio.ax.model.PersonAttachmentMetadata;
import ru.portfolio.ax.model.dto.LinkedObjectDTO;
import ru.portfolio.ax.model.ref.DataSourceRef;
import ru.portfolio.ax.model.ref.DisciplineRef;
import ru.portfolio.ax.model.ref.SectionRef;
import ru.portfolio.ax.model.ref.SubjectsRef;
import ru.portfolio.ax.repository.PersonAttachmentMetadataRepository;
import ru.portfolio.ax.rest.dto.PersonAuthPermission;
import ru.portfolio.ax.service.CrudService;
import ru.portfolio.ax.util.Utils;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_EMPTY;

@Data
@MappedSuperclass
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public abstract class PersonallyEntity extends AbstractEntity<Long> implements PersonAuthPermission, Linkable {
    @NotNull
    private String personId;

    @Transient
    private JsonNode fileReferences;

    @CreatedDate
    @ApiModelProperty(hidden = true)
    @Column(nullable = false, updatable = false)
    private LocalDateTime creationDate;

    @LastModifiedDate
    @ApiModelProperty(hidden = true)
    private LocalDateTime editDate;

    @NotNull
    private Integer dataKind;

    @ApiModelProperty(hidden = true)
    private String hashCode;
    @NotNull
    private String creatorId;

    @NotNull
    private Boolean isImport;

    @NotNull
    private String name;

    private Boolean isDelete;

    @NotNull
    @ManyToOne
    private DataSourceRef source;

    @Transient
    private List<LinkedObjectDTO> linkedObjects;

    public SectionRef getCategory() {
        return null;
    }

    public Integer getCategoryCode() {
        return Utils.safeGet(getCategory(), SectionRef::getCode);
    }

    @Override
    @JsonAnyGetter
    public Map<String, Object> getMap() {
        return super.getMap();
    }

    @Override
    public void validate(CrudService crudService) {
        crudService.existAllRef(Utils.splitTo(getSubjectsCode(), Integer::valueOf), SubjectsRef.class);
        crudService.existAllRef(Utils.splitTo(getDisciplineCode(), Integer::valueOf), DisciplineRef.class);
    }

    @Override
    public void reachTransient(CrudService crudService) {
        Utils.reachRefs2map(map, getSubjectsCode(), SubjectsRef.class, crudService);
        Utils.reachRefs2map(map, getDisciplineCode(), DisciplineRef.class, crudService);

        PersonAttachmentMetadataRepository repository = crudService
                .getRepository(PersonAttachmentMetadata.class);

        setFileReferences(crudService.getObjectMapper().valueToTree(
                repository.findAllByEntityIdAndEntityTypeAndDeleted(
                        getId(), getClass().getSimpleName(), Boolean.FALSE
                )));
    }

    @JsonInclude(NON_EMPTY)
    public String getDisciplineCode() {
        return StringUtils.EMPTY;
    }

    @JsonInclude(NON_EMPTY)
    public String getSubjectsCode() {
        return StringUtils.EMPTY;
    }
}