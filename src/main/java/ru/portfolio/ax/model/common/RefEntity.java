package ru.portfolio.ax.model.common;

import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.reflect.ConstructorUtils;

import javax.persistence.MappedSuperclass;
import java.util.function.Supplier;

@Data
@MappedSuperclass
@FieldNameConstants
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class RefEntity extends AbstractRefEntity<Integer> {

    private String value;

    @NoArgsConstructor(access = AccessLevel.PROTECTED)
    public static class Fields extends AbstractRefEntity.Fields{}

    @SneakyThrows
    public static <E extends RefEntity> E buildByValue(Class<E> clazz, String ref) {
        E constructor = ConstructorUtils.invokeConstructor(clazz);
        return buildByValue(() -> constructor, ref);
    }

    private static <E extends RefEntity> E buildByValue(Supplier<E> supplier, String ref) {
        E refEntity = supplier.get();
        refEntity.setCode(0);
        refEntity.setValue(ref);
        return refEntity;
    }

    @SneakyThrows
    public static <E extends RefEntity> E build(Class<E> clazz, Integer ref) {
        E constructor = ConstructorUtils.invokeConstructor(clazz);
        return build(() -> constructor, ref);
    }

    private static <E extends RefEntity> E build(Supplier<E> supplier, Integer ref) {
        E refEntity = supplier.get();
        refEntity.setCode(ref);
        refEntity.setValue(String.valueOf(ref));
        return refEntity;
    }
}
