package ru.portfolio.ax.model.common;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

import javax.persistence.MappedSuperclass;

@Data
@MappedSuperclass
@FieldNameConstants
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class RefWithParentEntity extends RefEntity {
    private Integer parentId;
}
