package ru.portfolio.ax.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import ru.portfolio.ax.model.common.AbstractEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import java.util.UUID;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class UserVisibilitySetting extends AbstractEntity<Long> {

    @Column(name = "person_id")
    private UUID personId;

    @ManyToOne
    private User user;

    private String section;

    @Column(name = "record_id")
    private Long recordId;

    @Column(name = "record_uuid")
    private UUID recordUuid;

    public UserVisibilitySetting(UUID personId, User user, String section, Long recordId) {
        this.personId = personId;
        this.user = user;
        this.section = section;
        this.recordId = recordId;
    }

    public UserVisibilitySetting(UUID personId, User user, String section, UUID recordUuid) {
        this.personId = personId;
        this.user = user;
        this.section = section;
        this.recordUuid = recordUuid;
    }
}
