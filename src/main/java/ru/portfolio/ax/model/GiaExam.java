package ru.portfolio.ax.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import ru.portfolio.ax.model.common.AbstractEntity;
import ru.portfolio.ax.model.ref.ExamFormRef;
import ru.portfolio.ax.model.ref.ExamKindRef;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@Entity
@FieldNameConstants
@Where(clause = "is_deleted = false")
@EqualsAndHashCode(callSuper = true)
public class GiaExam extends AbstractEntity<Long> {
    private String personId;
    private LocalDate eventDate;
    private LocalTime eventTime;
    @CreatedDate
    @ApiModelProperty(hidden = true)
    @Column(nullable = false, updatable = false)
    private LocalDateTime creationDate;
    @LastModifiedDate
    @ApiModelProperty(hidden = true)
    private LocalDateTime updateDate;
    private Long subjectId;
    private String subjectName;
    @ManyToOne
    private ExamFormRef examForm;
    private Integer primaryMarkValue;
    private Integer primaryMarkBasis;
    private String normalizedMarkValue;
    private String normalizedMarkBasis;
    private Integer positiveResultThreshold;
    private Integer percentMarkValue;
    private Boolean isCredit;
    private Boolean isDeleted;
    private Long timeStamp;
    private Long examId;
    private Integer examVariant;
    @Column(name = "score_a")
    private Integer scoreA;
    @Column(name = "score_b")
    private Integer scoreB;
    @Column(name = "score_c")
    private Integer scoreC;
    @Column(name = "score_d")
    private Integer scoreD;
    private Boolean approbation;
    @ManyToOne
    private ExamKindRef examKind;
}