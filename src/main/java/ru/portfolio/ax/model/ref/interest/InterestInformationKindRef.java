package ru.portfolio.ax.model.ref.interest;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import ru.portfolio.ax.model.common.InterestRefEntity;

import javax.persistence.Entity;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties( { "id" })
public class InterestInformationKindRef extends InterestRefEntity {
}
