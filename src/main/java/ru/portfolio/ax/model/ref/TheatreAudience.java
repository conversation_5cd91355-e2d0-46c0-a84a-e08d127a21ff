package ru.portfolio.ax.model.ref;

import lombok.Data;
import lombok.EqualsAndHashCode;
import ru.portfolio.ax.model.common.AbstractRefEntity;
import ru.portfolio.ax.model.common.RefEntity;

import javax.persistence.Entity;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
public class TheatreAudience extends AbstractRefEntity<Integer> {
    private Integer theatryCode;
    private Integer audienceCode;
}
