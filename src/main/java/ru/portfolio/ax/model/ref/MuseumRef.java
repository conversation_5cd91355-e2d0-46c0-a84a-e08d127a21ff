package ru.portfolio.ax.model.ref;

import lombok.Data;
import lombok.EqualsAndHashCode;
import ru.portfolio.ax.model.common.RefEntity;
import ru.portfolio.ax.repository.ref.CheckInEntities;
import ru.portfolio.ax.rest.dto.NearbyOrganizationsDTO;

import javax.persistence.Column;
import javax.persistence.Entity;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
public class MuseumRef extends RefEntity implements CheckInEntities {
    private String address;
    @Column(name = "geocode_x")
    private Double geocodeX;
    @Column(name = "geocode_y")
    private Double geocodeY;
    private Boolean moskvenok;
    private String city;
    private String street;
    private String note;
    private String description;
    private String webSite;
    private String email;
    private Boolean isArchive;

    public Integer getType() {
        return 1;
    }

    @Override
    public NearbyOrganizationsDTO.Organization toOrganization() {
        NearbyOrganizationsDTO.Organization org = new NearbyOrganizationsDTO.Organization();
        org.setCode(this.getCode());
        org.setName(this.getValue());
        org.setGeocodeX(this.getGeocodeX());
        org.setGeocodeY(this.getGeocodeY());
        org.setAddress(this.getAddress());
        org.setType(this.getType());
        return org;
    }
}
