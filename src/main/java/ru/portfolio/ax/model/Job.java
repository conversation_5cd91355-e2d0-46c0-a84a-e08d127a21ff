package ru.portfolio.ax.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import ru.portfolio.ax.model.common.HasCategoryCode;
import ru.portfolio.ax.model.common.Personalized;
import ru.portfolio.ax.model.common.PersonallyEntity;
import ru.portfolio.ax.model.ref.EmploymentDocTypeRef;
import ru.portfolio.ax.model.ref.LevelBusinessRef;
import ru.portfolio.ax.model.ref.SalaryRangeRef;
import ru.portfolio.ax.model.ref.SectionRef;
import ru.portfolio.ax.rest.interfaces.AuditableHistoryEntity;
import ru.portfolio.ax.util.Utils;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@AttributeOverrides({
        @AttributeOverride(name = "name", column = @Column(name = "position", insertable = false, updatable = false)),
        @AttributeOverride(name = "dataKind", column = @Column(name = "data_kind_code"))
})
public class Job extends PersonallyEntity implements AuditableHistoryEntity, Personalized, HasCategoryCode {

    private String eventId;
    private String recordId;

    @NotNull
    @ManyToOne
    private SectionRef category;

    @NotNull
    @ManyToOne
    private SectionRef type;
    @ManyToOne
    private LevelBusinessRef businessLevel;
    @ManyToOne
    private SalaryRangeRef salaryRange;
    @ManyToOne
    private EmploymentDocTypeRef contractType;
    @NotNull
    private String position;
    @NotNull
    private String mainFunctionality;

    private Boolean isContract;
    private LocalDate contractDate;

    private LocalDate expulsionDate;
    private LocalDate graduationDate;

    private Boolean isByProfile;
    private String placement;
    private String organization;

    @Transient
    private final Integer entityTypeCode = 8;

    @Override
    public String buildActionHistoryDescription() {
        return "Job с id = " + this.getId();
    }

    public Integer getCategoryCode() {
        return Utils.safeGet(getCategory(), SectionRef::getCode);
    }
}