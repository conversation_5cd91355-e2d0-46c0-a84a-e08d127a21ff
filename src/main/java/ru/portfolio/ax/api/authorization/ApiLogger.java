package ru.portfolio.ax.api.authorization;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import ru.portfolio.ax.api.domain.model.AccessOptions;
import ru.portfolio.ax.api.domain.model.ApiLog;
import ru.portfolio.ax.api.domain.repository.ApiLogRepository;

import java.time.LocalDateTime;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApiLogger {

    private final ApiLogRepository apiLogRepository;

    private final ObjectMapper objectMapper;

    public void logRequest(String url, AccessOptions accessOptions, Object request) {

        ApiLog apiLog = new ApiLog();
        apiLog.setAccessOptions(accessOptions);
        apiLog.setRequestBody(objectMapper.valueToTree(request));
        apiLog.setRequestUrl(url);
        apiLog.setActionDate(LocalDateTime.now());

        apiLogRepository.save(apiLog);
    }
}
