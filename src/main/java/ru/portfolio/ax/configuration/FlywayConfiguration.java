package ru.portfolio.ax.configuration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.configuration.FluentConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Configuration
@AllArgsConstructor
public class FlywayConfiguration {

    @Bean(initMethod = "migrate")
    public Flyway flyway(DataSource dataSource,
                         @Value("${spring.datasource.hikari.schema}") String schema,
                         @Value("${migration.regions}") Boolean isRegion) {
        FluentConfiguration configuration = new FluentConfiguration();

        //Параметр позволяет выполнять миграции не по порядку
        //Если существуют выполненные версии миграций 1 и 3, то при добавлении версии 2, она не будет пропущена
        configuration.outOfOrder(true);

        //Позволяет выполнять миграции на уже существующей бд, которая не мигрировалась Flyway'ем
        configuration.baselineOnMigrate(true);

        //Схемы, в которые будут выполнены миграции
        configuration.schemas(schema);
        configuration.dataSource(dataSource);
        //Проверка хэшей уже выполненных миграций
        configuration.validateOnMigrate(true);
        //Не очищать бд, если обнаруженны ошибки валидации
        configuration.cleanOnValidationError(false);

        //Начальная точка отсчёта версий миграций
        //  configuration.baselineVersion("1000");
        //Пути к файлам миграций
        String region = isRegion ? "db/regions" : "db/msk";
        configuration.locations("db/migration", region);
        configuration.ignoreMissingMigrations(true);
        configuration.ignoreFutureMigrations(true);

        return new Flyway(configuration);
    }
}