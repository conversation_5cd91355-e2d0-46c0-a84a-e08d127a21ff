alter table portfolio.share_link
	add olympiads boolean default true not null;

alter table portfolio.share_link
	add sport_games boolean default true not null;

alter table portfolio.share_link
	add hike boolean default true not null;

alter table portfolio.share_link
	add creation_contest boolean default true not null;

alter table portfolio.share_link
	add civil_contest boolean default true not null;

alter table portfolio.share_link
	add offline_visit boolean default true not null;

alter table portfolio.share_link
	add online_visit boolean default true not null;

alter table portfolio.share_link
	add science_contest boolean default true not null;

alter table portfolio.share_link
	add sport_unit boolean default true not null;

alter table portfolio.share_link
	add creation_unit boolean default true not null;

alter table portfolio.share_link
	add civil_unit boolean default true not null;

alter table portfolio.share_link
	add science_employments boolean default true not null;

alter table portfolio.share_link
	add sport_reward boolean default true not null;

alter table portfolio.share_link
	add creation_reward boolean default true not null;

alter table portfolio.share_link
	add civil_reward boolean default true not null;

alter table portfolio.share_link
	add science_reward boolean default true not null;

alter table portfolio.share_link
	add sport_club boolean default true not null;

alter table portfolio.share_link
	add creation_club boolean default true not null;

alter table portfolio.share_link
	add civil_club boolean default true not null;

alter table portfolio.share_link
	add projects boolean default true not null;

alter table portfolio.share_link
	add gia boolean default true not null;

alter table portfolio.share_link
	add ege boolean default true not null;

alter table portfolio.share_link
	add oge boolean default true not null;

alter table portfolio.share_link
	add gve9 boolean default true not null;

alter table portfolio.share_link
	add gve11 boolean default true not null;

alter table portfolio.share_link
	add other boolean default true not null;

alter table portfolio.share_link
	add diagnostic boolean default true not null;