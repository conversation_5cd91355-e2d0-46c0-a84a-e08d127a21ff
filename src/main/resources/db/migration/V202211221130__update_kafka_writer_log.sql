alter table portfolio.kafka_writer_log drop column person_id;
alter table portfolio.kafka_writer_log rename column event_type_code to kafka_event_code;
alter table portfolio.kafka_writer_log drop column source_message_code;
alter table portfolio.kafka_writer_log add column message_data jsonb;
alter table portfolio.kafka_writer_log drop column record_id;
alter table portfolio.kafka_writer_log add modification_date timestamp;
alter table portfolio.kafka_writer_log alter column error_message type text using error_message::text;
alter table portfolio.kafka_writer_log add topic_name varchar(255);
alter table portfolio.kafka_writer_log drop column subsystem_code;
alter table portfolio.kafka_writer_log add retry_count integer;

