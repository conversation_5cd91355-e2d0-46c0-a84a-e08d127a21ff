insert into portfolio.param_validation_ref
(code,
 kafka_event_code,
 param_name,
 param_value,
 portfolio_value,
 is_not_null,
 param_type_code)
values (153,	7,	'groupId',	NULL,	NULL,	TRUE,	2),
       (154,	7,	'lesson.themeType',	NULL,	NULL,	TRUE,	2);

delete from portfolio.skip_lesson_reason_ref where code = 4;
insert into portfolio.skip_lesson_reason_ref
(code,
 value,
 is_archive)
values
    (4,	'Карантин',	FALSE),
    (5,	'Иное (неуважительная или сомнительная причина)',	FALSE);