UPDATE portfolio.section_settings_ref
SET value = 'Профориентационные мероприятия'
WHERE code = 58;

ALTER TABLE portfolio.share_link
    RENAME COLUMN profession_prof_tests TO profession_prof_events;

INSERT INTO portfolio.section_settings_ref (code, value, parent_id, is_archive)
VALUES
    (60, 'Рекомендации/Профориентация', 38, false),
    (61, 'Рекомендации/Пробы', 38, false),
    (62, 'Рекомендации/Экскурсии к работодателю', 38, false),
    (63, 'Рекомендации/Дни открытых дверей', 38, false),
    (64, 'Профориентационные мероприятия/Пробы', 38, false),
    (65, 'Профориентационные мероприятия/Экскурсии к работодателю', 38, false),
    (66, 'Профориентационные мероприятия/Дни открытых дверей', 38, false);