INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (1, 'Водная', null);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (2, 'Каяк', 1);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (3, 'Байдарка', 1);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (4, 'Катамаран 2', 1);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (5, 'Катамаран 4', 1);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (6, 'Командная гонка', 1);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (7, 'Комбинированная', null);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (8, 'Индивидуально', 7);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (9, 'Лыжная', null);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (10, 'Индивидуально', 9);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (11, 'Связка', 9);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (12, 'Группа', 9);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (13, 'На средствах передвижения', null);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (14, 'Индивидуально', 13);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (15, 'Группа', 13);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (16, 'Парусная', null);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (17, 'Индивидуально', 16);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (18, 'Пешеходная', null);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (19, 'Индивидуально', 18);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (20, 'Связка', 18);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (21, 'Группа', 18);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (22, 'Спелео', null);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (23, 'Индивидуально', 22);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (24, 'Связка', 22);
INSERT INTO tourism_kind_ref (code, value, parent_id) VALUES (25, 'Группа', 22);
