create index if not exists user_setting_person_id_index
    on portfolio.user_setting (person_id);

create index if not exists user_setting_user_id_index
    on portfolio.user_setting (user_id);

create index if not exists interest_person_id_index
    on portfolio.interest (person_id);

create index if not exists learner_history_person_id_index
    on portfolio.learner_history (person_id);

create index if not exists user_view_function_person_id_index
    on portfolio.user_view_function (person_id);

create index if not exists independent_diagnostic_visible_person_id_index
    on portfolio.independent_diagnostic_visible (person_id);

create index if not exists independent_diagnostic_visible_record_id_index
    on portfolio.independent_diagnostic_visible (record_id);

create index if not exists excel_cache_uuid_index
    on portfolio.excel_cache (uuid);

create index if not exists change_history_person_id_index
    on portfolio.change_history (person_id);

create index if not exists change_history_user_id_index
    on portfolio.change_history (user_id);

create index if not exists change_history_creator_id_index
    on portfolio.change_history (creator_id);

create index if not exists import_history_creator_id_index
    on portfolio.import_history (creator_id);