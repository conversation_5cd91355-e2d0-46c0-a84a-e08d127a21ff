create table portfolio.action_kind_ref
(
    code  smallint
        constraint action_kind_ref_ref_pk
            primary key,
    value text
);

INSERT INTO "portfolio"."action_kind_ref" ("code", "value")
VALUES (1, 'Загрузка данных в портфолио');
INSERT INTO "portfolio"."action_kind_ref" ("code", "value")
VALUES (2, 'Редактирование данных');
INSERT INTO "portfolio"."action_kind_ref" ("code", "value")
VALUES (3, 'Удаление данных');
INSERT INTO "portfolio"."action_kind_ref" ("code", "value")
VALUES (4, 'Генерация ссылки (и QR-кода) для предоставления доступа к портфолио учащегося');
INSERT INTO "portfolio"."action_kind_ref" ("code", "value")
VALUES (5, 'Изменение настроек отображения портфолио');
INSERT INTO "portfolio"."action_kind_ref" ("code", "value")
VALUES (6, 'Сохранение файла-шаблона для загрузки данных в портфолио учащихся');
INSERT INTO "portfolio"."action_kind_ref" ("code", "value")
VALUES (7, 'Загрузка файла для добавления данных нескольких учащихся');