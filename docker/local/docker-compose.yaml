services:
  postgres:
    image: postgres:14.2
    container_name: postgres
    environment:
      POSTGRES_DB: "portfolio"
      POSTGRES_USER: ""
      POSTGRES_PASSWORD: ""
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  clickhouse:
    image: clickhouse/clickhouse-server:latest
    container_name: clickhouse
    ports:
      - "8123:8123"  # HTTP
      - "9000:9000"  # Native
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - ./clickhouse/init:/docker-entrypoint-initdb.d
    environment:
      CLICKHOUSE_USER: ""
      CLICKHOUSE_PASSWORD: ""

volumes:
  postgres_data:
  clickhouse_data:
