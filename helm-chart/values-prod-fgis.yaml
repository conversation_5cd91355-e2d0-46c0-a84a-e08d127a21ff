env: prod-fgis
tier: backend

deployment:
  deploymentAnnotations:
  replicaCount: 1
  image:
    repository: harbor-edu.mos.ru/mes-portfolio/portfolio_back
    tag: "latest"
    pullPolicy: IfNotPresent
  port: 8080
  resources:
    #limits:
    #  cpu: 2
    #  memory: 2048Mi
    requests:
      cpu: 1
      memory: 2Gi
  restartPolicy: Always
  volume: |-
    - name: {{ .Chart.Name }}
      configMap:
        name: {{ .Chart.Name }}
        defaultMode: 0777
        items:
          - key: run.sh
            path: run.sh
  volumeMounts: |-
    - name: {{ .Chart.Name }}
      mountPath: /app/run.sh
      subPath: run.sh

  livenessProbe:
   path: /app/actuator/health/
   port: 8080
   initDelay: 30
   failThreshold: 3
  readinessProbe:
   path: /app/actuator/health/
   port: 8080
   initDelay: 30
   failThreshold: 3
   readnessPeriod: 5

service:
  type: ClusterIP
  port: 80
  targetPort: 8080

ingress:
  enabled: true
  name: portfolio
  url: kub-lb01p.astra.mesh.sitronics.com
  pathType: Prefix
  path: /portfolio
  servicePort: 80

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 75
  targetMemoryUtilizationPercentage: 75

configmap:
  application:
    JAR_APP_PATH: /app/portfolio.jar
    db_host: **********
    replica_db_host: *********
    db_pass: 'TayyRfs1'
    clickhouse_host: *********
    click_login: 'portfolio'
    click_pass: 'epLHW6EQHNjm'
    click_logs_state: false
    click_mv_state: true
    contingent_api_url:  https://mes-api-kaluga.mos.ru/contingent
    contingent_api_key: 'e52a9570-038f-4695-9b41-ac725e421d76'
    contingent_source:  https://mes-api-kaluga.mos.ru/contingent/persons/
    esz_host:
    ESZ_X_API_KEY:
    ESZ_KKY:
    IMPORT_FILE_MAX_ROWS_COUNT: 10000
    IMPORT_DATA_PARTITION_SIZE: 500
    nsi_api_url: https://mes-api-kaluga.mos.ru/nsi/catalog/get
    nsi_api_key: 'c14f7136-59dc-48ab-9cc3-a10a72fee3ee'
    portfolio_link_qr_code: 'https://portfolio-kaluga.mos.ru/shared-link/'
    contingent_classes_host_url: https://mes-api-kaluga.mos.ru/contingent/classes/
    aupd_url: https://mes-api-kaluga.mos.ru/aupd/v1/
    aupd_api_key: df1e841f-5c33-42ae-8083-af3cd7217f84
    aupd_token: https://school-kaluga.mos.ru/
    aupd_cert_name: key.cer
    s3_key_id: u8XAJ3pGfoo3tPDq
    s3_key_secret: lv3yTrGbydUIyUaA0ED50viMeiuWj1Q5
    s3_bucket_name: p-student
    s3_address: **********:9000
    cron_update_job: "0 0 23 ? * *"
    KAFKA_EDUCATION_TOPIC: education.fct.uchi-contest-result.1
    KAFKA_EDUCATION_GROUP_ID: portfolio_group
    KAFKA_USERNAME: portfolio
    KAFKA_PASSWORD: xjth3AE3Rw
    KAFKA_BOOTSTRAP_SERVERS: *********:9092,*********:9092,*********:9092
    KAFKA_TOPIC: messages.from.any.to.eks
    KAFKA_ENABLED: false
    CONNECTION_POOL_SIZE: 60
    NOTIFICATIONS_CLICKHOUSE_DAYS_OLD: 1
    REST_TIMEOUT_TIME: 10000
    REST_READ_TIMEOUT_TIME: 10000
    REST_TIMEOUT_TIME_LOW: 5000
    REST_READ_TIMEOUT_TIME_LOW: 5000
    REST_TIMEOUT_TIME_HIGH: 30000
    REST_READ_TIMEOUT_TIME_HIGH: 30000
    MIGRATION_REGIONS: true
    SCHOOL_HOST: https://school-kaluga.mos.ru/avatars/
    SCHOOL_RATING_ENABLED: true
    KAFKA_MAX_POLL_RECORDS: 10
    KAFKA_MAX_POLL_INTERVAL_MS: 300000
    KAFKA_SESSION_TIMEOUT_MS: 45000
    KAFKA_EDUCATION_AUTO_STARTUP: true
    proforientation_api_key: 64bb6439-42cf-4c03-8ec8-1a36937e7c23
